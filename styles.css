body {
    width: 100%;
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #ffffff;
    color: #333333;
    line-height: 1.5;
    font-size: 12px;
    overflow-x: hidden;
}

.container {
    padding: 12px;
    max-width: 100%;
    background: #ffffff;
    height: auto;
}

h2 {
    margin: 0 0 8px 0;
    color: #333333;
    font-size: 16px;
    font-weight: 600;
    text-align: left;
    display: flex;
    align-items: center;
    gap: 6px;
}

h2::before {
    content: "🔗";
    font-size: 14px;
}

/* 标签页导航 */
.tab-navigation {
    display: flex;
    background: #ffffff;
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: 6px;
}

.tab-btn {
    flex: 1;
    padding: 2px 2px;
    border: none;
    background-color: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    text-align: center;
    position: relative;
    border-bottom: 2px solid transparent;
}

.tab-btn:hover {
    background-color: #f8f9fa;
}

.tab-btn.active {
    border-bottom-color: #4285f4;
}

.tab-title {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: #333333;
    margin-bottom: 2px;
}

.tab-btn.active .tab-title {
    color: #225ebe;
}

.tab-subtitle {
    display: block;
    font-size: 14px;
    color: #111111;
    font-weight: 500;
}

.tab-btn.active .tab-subtitle {
    color: #4285f4;
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

p {
    margin: 0 0 6px 0;
    color: #666666;
    font-size: 11px;
    text-align: left;
    font-weight: 400;
    line-height: 1.4;
}

#urlInput {
    width: 100%;
    height: 400px;
    padding: 10px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 12px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    resize: none;
    box-sizing: border-box;
    background: #ffffff;
    transition: border-color 0.2s ease;
    line-height: 1.4;
    overflow-y: auto;
}

#urlInput:focus {
    outline: none;
    border-color: #4285f4;
    box-shadow: 0 0 0 1px #4285f4;
}

.button-group {
    display: flex;
    gap: 4px;
    margin-top: 6px;
    flex-wrap: wrap;
}

.button-group-secondary {
    margin-top: 8px;
}

/* Root Analysis按钮行样式 - 确保2个按钮在一行 */
.root-button-row {
    flex-wrap: nowrap; /* 防止按钮换行 */
}

.root-button-row .btn {
    min-width: 80px; /* 设置最小宽度 */
    white-space: nowrap; /* 防止文字换行 */
}

.btn {
    flex: 1;
    min-width: 70px;
    padding: 6px 4px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: rgb(89, 89, 89);
    color: white;
}

.btn:hover {
    background-color: #f8f9fa;
}

.btn-primary {
    background: rgb(89, 89, 89);
    color: #ffffff;
    border-color: rgb(89, 89, 89);
}

.btn-primary:hover {
    background: rgb(36, 144, 135);
    border-color: rgb(36, 144, 135);
}

.btn-secondary {
    background: rgb(89, 89, 89);
    color: #ffffff;
    border-color: rgb(89, 89, 89);
}

.btn-secondary:hover {
    background: rgb(36, 144, 135);
    border-color: rgb(36, 144, 135);
}

.btn-warning {
    background: rgb(89, 89, 89);
    color: #ffffff;
    border-color: rgb(89, 89, 89);
}

.btn-warning:hover {
    background: rgb(36, 144, 135);
    border-color: rgb(36, 144, 135);
}

.btn-warning:disabled {
    background: #d1d5db;
    color: #9ca3af;
    border-color: #d1d5db;
    cursor: not-allowed;
}

.status {
    margin-top: 12px;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 11px;
    text-align: center;
    min-height: 12px;
    font-weight: 400;
    transition: all 0.2s ease;
}

.status.success {
    background: #f0f9ff;
    color: #059669;
    border: 1px solid #bfdbfe;
}

.status.error {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.status.info {
    background: #eff6ff;
    color: #2563eb;
    border: 1px solid #bfdbfe;
}

/* 文本处理区域 */

#textInput {
    width: 100%;
    height: 80px;
    padding: 6px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 12px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    resize: none;
    box-sizing: border-box;
    margin-top: 6px;
    /* margin-bottom: 6px; */
    background: #ffffff;
    transition: border-color 0.2s ease;
    line-height: 1.4;
    overflow-y: auto;
}

#textInput:focus {
    outline: none;
    border-color: #4285f4;
    box-shadow: 0 0 0 1px #4285f4;
}

/* 对比词输入框样式 */
.compare-word-container {
    display: flex;
    align-items: center;
    margin-top: 6px;
    /* margin-bottom: 6px; */
    gap: 6px;
}

/* 日期范围容器样式 */
.date-range-container {
    display: flex;
    align-items: center;
    margin-top: 6px;
    margin-bottom: 6px;
    gap: 6px;
}

.compare-word-label {
    font-size: 11px;
    color: #111111;
    font-weight: 500;
    min-width: 50px;
}

.compare-word-select {
    flex: 1;
    min-width: 150px;
    padding: 6px 6px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 12px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #ffffff;
    transition: border-color 0.2s ease;
}

.compare-word-select:focus {
    outline: none;
    border-color: #4285f4;
    box-shadow: 0 0 0 1px #4285f4;
}

.date-range-label {
    font-size: 11px;
    color: #111111;
    font-weight: 500;
    min-width: 40px;
}

.date-range-select {
    flex: 1;
    padding: 6px 6px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 12px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #ffffff;
    transition: border-color 0.2s ease;
    min-width: 120px;
}

.date-range-select:focus {
    outline: none;
    border-color: #4285f4;
    box-shadow: 0 0 0 1px #4285f4;
}

/* 右键添加文本开关样式 */
.right-click-container {
    display: flex;
    align-items: center;
    margin-top: 6px;
    margin-bottom: 6px;
    gap: 12px;
    flex-wrap: wrap;
}

.right-click-label {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    font-size: 12px;
    color: #111111;
    font-weight: 500;
}

.right-click-checkbox {
    width: 14px;
    height: 14px;
    cursor: pointer;
}

.right-click-text {
    user-select: none;
}

/* 进度显示样式 */
.progress-container {
    display: flex;
    align-items: center;
    margin-top: 8px;
    margin-bottom: 6px;
    gap: 6px;
    padding: 6px 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e5e5e5;
}

.progress-label {
    font-size: 12px;
    color: #666666;
    font-weight: 500;
}

.progress-display {
    font-size: 12px;
    color: #4285f4;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.progress-hidden {
    display: none !important;
}

/* Root Analysis 样式 */
.root-input-container {
    display: flex;
    align-items: center;
    margin-top: 6px;
    margin-bottom: 12px;
    gap: 6px;
    flex-wrap: wrap;
}

.root-input-container .btn {
    margin-left: 4px;
}

.root-input-label {
    font-size: 12px;
    color: #111111;
    font-weight: 500;
    white-space: nowrap;
}

.root-input {
    width: 30px;
    padding: 4px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 12px;
    text-align: center;
    -webkit-appearance: none;
    -moz-appearance: textfield;
    appearance: none;
}

/* 隐藏Chrome/Safari中的数字输入框箭头 */
.root-input::-webkit-outer-spin-button,
.root-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* 隐藏Firefox中的数字输入框箭头 */
.root-input[type=number] {
    -moz-appearance: textfield;
    appearance: textfield;
}

.root-input:focus {
    outline: none;
    border-color: #4285f4;
    box-shadow: 0 0 0 1px #4285f4;
}

.root-words-list {
    margin-top: 12px;
    margin-bottom: 12px;
}

.root-word-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding: 8px;
    border-radius: 4px;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    transition: background-color 0.2s ease;
}

.root-word-row.opened {
    background-color: #dbeafe;
    border-color: #93c5fd;
}

.root-word-index {
    font-size: 12px;
    font-weight: 600;
    color: #6b7280;
    min-width: 20px;
    margin-right: 3px;
}

.root-word-item {
    font-size: 12px;
    color: #000000;
    cursor: pointer;
    padding: 2px 2px;
    border-radius: 3px;
    transition: background-color 0.2s ease;
    margin-right: 2px;
    font-weight: 500;
}

.root-word-item:hover {
    background-color: #f3f4f6;
}

.root-edit-container {
    display: flex;
    justify-content: center;
    margin-top: 12px;
}

/* 编辑模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #ffffff;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #111111;
}

.modal-close {
    font-size: 24px;
    font-weight: bold;
    color: #6b7280;
    cursor: pointer;
    line-height: 1;
}

.modal-close:hover {
    color: #111111;
}

.modal-body {
    padding: 20px;
}

.modal-body p {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #374151;
}

.modal-body textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 12px;
    font-family: monospace;
    resize: vertical;
    line-height: 1.4;
}

.modal-body textarea:focus {
    outline: none;
    border-color: #4285f4;
    box-shadow: 0 0 0 1px #4285f4;
}

.modal-body textarea.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 1px #ef4444;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding: 16px 20px;
    border-top: 1px solid #e5e7eb;
}

/* 处理结果区域 */
.processed-result {
    margin-top: 6px;
    padding: 6px;
    background: #ffffff;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    display: none;
}

.processed-result.show {
    display: block;
}

.processed-result h4 {
    margin: 0 0 6px 0;
    color: #333333;
    font-size: 12px;
    font-weight: 600;
}

.result-content {
    overflow-y: visible;
    transition: max-height 0.3s ease;
}

/* 自定义滚动条样式 */
.result-content::-webkit-scrollbar {
    width: 6px;
}

.result-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.result-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.result-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 词条列表样式 */
.word-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 2px;
    margin-bottom: 4px;
    background: #e1e0e0;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.word-item:hover {
    background: #f8f9fa;
    border-color: #e5e5e5;
}

.word-text {
    flex: 1;
    font-size: 12px;
    color: #333333;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    margin-right: 6px;
    font-weight: 400;
    padding-left: 4px;
}

.word-actions {
    display: flex;
    gap: 4px;
    padding-right: 2px;
}

.action-btn {
    width: 20px;
    height: 20px;
    border: none;
    border-radius: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.2s ease;
    background: transparent;
    padding: 0;
}

.action-btn .icon {
    width: 18px;
    height: 18px;
    stroke: currentColor;
    flex-shrink: 0;
}

.action-btn:hover {
    background: transparent;
    opacity: 0.7;
}

.copy-btn {
    color: #4285f4;
}

.copy-btn:hover {
    background: transparent;
    opacity: 0.7;
}

.copy-btn .icon {
    stroke: #4285f4;
}

.delete-btn {
    color: #dc2626;
}

.delete-btn:hover {
    background: transparent;
    opacity: 0.7;
}

.delete-btn .icon {
    stroke: #dc2626;
}

.trends-btn {
    color: #f59e0b;
}

.trends-btn:hover {
    background: transparent;
    opacity: 0.7;
}

.trends-btn .icon {
    stroke: #f59e0b;
}

/* 空状态 */
.empty-result {
    text-align: center;
    color: #666666;
    font-style: normal;
    padding: 24px 16px;
    font-size: 14px;
    font-weight: 400;
}

/* Google搜索功能样式 */
.google-search-section {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e5e5e5;
}

.google-search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 12px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #ffffff;
    color: #333333;
    margin-bottom: 12px;
    box-sizing: border-box;
}

.google-search-input:focus {
    outline: none;
    border-color: rgb(36, 144, 135);
    box-shadow: 0 0 0 2px rgba(36, 144, 135, 0.1);
}

.google-search-controls {
    margin-bottom: 12px;
}

.google-search-checkbox-label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #333333;
    cursor: pointer;
}

.google-search-checkbox {
    margin: 0;
    cursor: pointer;
}

.google-search-checkbox-text {
    user-select: none;
}

.google-search-buttons {
    display: flex;
    gap: 8px;
}

.btn-google-search {
    flex: 1;
    min-width: 70px;
    padding: 6px 4px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: rgb(89, 89, 89);
    color: white;
    transition: all 0.2s ease;
}

.btn-google-search:hover {
    background: rgb(36, 144, 135);
    border-color: rgb(36, 144, 135);
}

.btn-google-search:active {
    transform: translateY(1px);
}

/* Ahrefs查询功能样式 */
.ahrefs-search-section {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e5e5e5;
}

.ahrefs-row {
    display: flex;
    gap: 4px;
    margin-bottom: 8px;
    align-items: center;
}

.ahrefs-btn {
    width: 40px !important; /* 固定宽度40px */
    min-width: 40px !important;
    max-width: 40px !important;
    flex-shrink: 0; /* 防止按钮缩小 */
    padding: 6px 2px;
    font-size: 10px;
    font-weight: 600;
    text-align: center;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    cursor: pointer;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: rgb(89, 89, 89);
    color: white;
    transition: all 0.2s ease;
    white-space: nowrap;
    overflow: hidden;
}

.ahrefs-btn:hover {
    background: rgb(36, 144, 135);
    border-color: rgb(36, 144, 135);
}

.ahrefs-btn:active {
    transform: translateY(1px);
}

.ahrefs-input {
    flex: 1; /* 填充剩余宽度 */
    padding: 6px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 12px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #ffffff;
    color: #333333;
    box-sizing: border-box;
}

.ahrefs-input:focus {
    outline: none;
    border-color: rgb(36, 144, 135);
    box-shadow: 0 0 0 2px rgba(36, 144, 135, 0.1);
}