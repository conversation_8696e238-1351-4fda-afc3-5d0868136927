<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google搜索功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .google-search-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 12px;
            margin-bottom: 12px;
            box-sizing: border-box;
        }
        .google-search-checkbox-label {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            margin-bottom: 12px;
        }
        .google-search-buttons {
            display: flex;
            gap: 8px;
        }
        .btn-google-search {
            flex: 1;
            padding: 6px 4px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            background: rgb(89, 89, 89);
            color: white;
        }
        .btn-google-search:hover {
            background: rgb(36, 144, 135);
        }
        .status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .status.error {
            background: #fee2e2;
            color: #dc2626;
        }
        .status.success {
            background: #d1fae5;
            color: #059669;
        }
    </style>
</head>
<body>
    <h1>Google搜索功能测试</h1>
    
    <div class="test-section">
        <h2>测试说明</h2>
        <p>这个页面用于测试Google搜索功能的URL构建逻辑。</p>
        <ul>
            <li>输入搜索内容</li>
            <li>选择是否添加引号</li>
            <li>点击不同的搜索按钮查看生成的URL</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Google搜索功能</h2>
        
        <input
            type="text"
            id="googleSearchInput"
            class="google-search-input"
            placeholder="输入搜索内容（用于Google搜索语法）"
        />
        
        <div class="google-search-controls">
            <label class="google-search-checkbox-label">
                <input type="checkbox" id="addQuotesCheckbox">
                <span>添加引号</span>
            </label>
        </div>

        <div class="google-search-buttons">
            <button id="siteSearchBtn" class="btn-google-search">site</button>
            <button id="intitleSearchBtn" class="btn-google-search">intitle</button>
            <button id="allintitleSearchBtn" class="btn-google-search">allintitle</button>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
        <div id="urlDisplay" style="margin-top: 10px; font-family: monospace; word-break: break-all;"></div>
    </div>

    <script>
        const googleSearchInput = document.getElementById('googleSearchInput');
        const addQuotesCheckbox = document.getElementById('addQuotesCheckbox');
        const siteSearchBtn = document.getElementById('siteSearchBtn');
        const intitleSearchBtn = document.getElementById('intitleSearchBtn');
        const allintitleSearchBtn = document.getElementById('allintitleSearchBtn');
        const status = document.getElementById('status');
        const urlDisplay = document.getElementById('urlDisplay');

        // 输入验证函数
        function validateGoogleSearchInput() {
            const searchText = googleSearchInput.value.trim();
            if (!searchText) {
                showStatus('请输入搜索内容', 'error');
                return false;
            }
            return true;
        }

        // 显示状态信息
        function showStatus(message, type) {
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }

        // 构建Google搜索URL
        function buildGoogleSearchUrl(searchType, searchText, addQuotes) {
            let query = '';
            const encodedText = encodeURIComponent(searchText);
            
            switch (searchType) {
                case 'site':
                    query = `site:${encodedText}`;
                    break;
                case 'intitle':
                    query = addQuotes ? `intitle:"${encodedText}"` : `intitle:${encodedText}`;
                    break;
                case 'allintitle':
                    query = addQuotes ? `allintitle:"${encodedText}"` : `allintitle:${encodedText}`;
                    break;
            }
            
            return `https://www.google.com/search?q=${encodeURIComponent(query)}`;
        }

        // 测试Google搜索
        function testGoogleSearch(searchType) {
            if (!validateGoogleSearchInput()) {
                return;
            }
            
            const searchText = googleSearchInput.value.trim();
            const addQuotes = addQuotesCheckbox.checked;
            const searchUrl = buildGoogleSearchUrl(searchType, searchText, addQuotes);
            
            urlDisplay.innerHTML = `<strong>生成的URL:</strong><br>${searchUrl}`;
            showStatus(`${searchType} 搜索URL已生成`, 'success');
            
            // 在新窗口中打开URL
            window.open(searchUrl, '_blank');
        }

        // 添加事件监听器
        siteSearchBtn.addEventListener('click', function() {
            testGoogleSearch('site');
        });

        intitleSearchBtn.addEventListener('click', function() {
            testGoogleSearch('intitle');
        });

        allintitleSearchBtn.addEventListener('click', function() {
            testGoogleSearch('allintitle');
        });
    </script>
</body>
</html>
