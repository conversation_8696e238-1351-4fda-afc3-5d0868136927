<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ahrefs查询功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .ahrefs-row {
            display: flex;
            gap: 4px;
            margin-bottom: 12px;
            align-items: center;
        }
        
        .ahrefs-btn {
            width: 40px !important;
            min-width: 40px !important;
            max-width: 40px !important;
            flex-shrink: 0;
            padding: 6px 2px;
            font-size: 10px;
            font-weight: 600;
            text-align: center;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            cursor: pointer;
            background: rgb(89, 89, 89);
            color: white;
            transition: all 0.2s ease;
            white-space: nowrap;
            overflow: hidden;
        }
        
        .ahrefs-btn:hover {
            background: rgb(36, 144, 135);
            border-color: rgb(36, 144, 135);
        }
        
        .ahrefs-input {
            flex: 1;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 12px;
            background: #ffffff;
            color: #333333;
            box-sizing: border-box;
        }
        
        .ahrefs-input:focus {
            outline: none;
            border-color: rgb(36, 144, 135);
            box-shadow: 0 0 0 2px rgba(36, 144, 135, 0.1);
        }
        
        .test-case {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            background: #f9f9f9;
        }
        
        .url-output {
            font-family: monospace;
            background: #fff;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            word-break: break-all;
            margin: 5px 0;
            font-size: 11px;
        }
        
        .expected {
            color: #059669;
        }
        
        .actual {
            color: #dc2626;
        }
        
        .correct {
            color: #059669;
        }
        
        .status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            display: none;
        }
        
        .status.error {
            background: #fee2e2;
            color: #dc2626;
        }
        
        .status.success {
            background: #d1fae5;
            color: #059669;
        }
    </style>
</head>
<body>
    <h1>Ahrefs查询功能测试</h1>
    
    <div class="test-container">
        <h2>功能测试区域</h2>
        
        <!-- KD查询行 -->
        <div class="ahrefs-row">
            <button id="kdSearchBtn" class="ahrefs-btn">KD</button>
            <input
                type="text"
                id="kdSearchInput"
                class="ahrefs-input"
                placeholder="输入关键词进行KD查询"
            />
        </div>

        <!-- Back Links查询行 -->
        <div class="ahrefs-row">
            <button id="backlinksSearchBtn" class="ahrefs-btn">BL</button>
            <input
                type="text"
                id="backlinksSearchInput"
                class="ahrefs-input"
                placeholder="输入域名进行反链查询"
            />
        </div>
        
        <div id="status" class="status"></div>
        <div id="urlDisplay" style="margin-top: 10px; font-family: monospace; word-break: break-all;"></div>
    </div>

    <div class="test-container">
        <h2>KD查询测试用例</h2>
        
        <div class="test-case">
            <h4>测试用例1：单个关键词</h4>
            <p><strong>输入：</strong>"ai"</p>
            <div class="url-output expected">期望：https://ahrefs.com/keyword-difficulty/?country=us&input=ai</div>
            <div class="url-output actual" id="kd-test1"></div>
        </div>
        
        <div class="test-case">
            <h4>测试用例2：包含空格的关键词</h4>
            <p><strong>输入：</strong>"ai image"</p>
            <div class="url-output expected">期望：https://ahrefs.com/keyword-difficulty/?country=us&input=ai%20image</div>
            <div class="url-output actual" id="kd-test2"></div>
        </div>
    </div>

    <div class="test-container">
        <h2>Back Links查询测试用例</h2>
        
        <div class="test-case">
            <h4>测试用例1：完整URL</h4>
            <p><strong>输入：</strong>"https://www.crazygames.com"</p>
            <div class="url-output expected">期望：https://ahrefs.com/backlink-checker/?input=crazygames.com&mode=subdomains</div>
            <div class="url-output actual" id="bl-test1"></div>
        </div>
        
        <div class="test-case">
            <h4>测试用例2：带www的域名</h4>
            <p><strong>输入：</strong>"www.crazygames.com"</p>
            <div class="url-output expected">期望：https://ahrefs.com/backlink-checker/?input=crazygames.com&mode=subdomains</div>
            <div class="url-output actual" id="bl-test2"></div>
        </div>
        
        <div class="test-case">
            <h4>测试用例3：纯域名</h4>
            <p><strong>输入：</strong>"crazygames.com"</p>
            <div class="url-output expected">期望：https://ahrefs.com/backlink-checker/?input=crazygames.com&mode=subdomains</div>
            <div class="url-output actual" id="bl-test3"></div>
        </div>
    </div>

    <script>
        // 复制扩展中的函数
        function normalizeDomain(input) {
            if (!input) return '';
            
            let domain = input.trim();
            
            // 移除协议前缀 (https://, http://)
            domain = domain.replace(/^https?:\/\//, '');
            
            // 移除www.前缀
            domain = domain.replace(/^www\./, '');
            
            // 移除末尾的路径、查询参数等
            domain = domain.split('/')[0];
            domain = domain.split('?')[0];
            domain = domain.split('#')[0];
            
            return domain;
        }

        function buildAhrefsUrl(type, input) {
            if (!input) return '';
            
            switch (type) {
                case 'kd':
                    return `https://ahrefs.com/keyword-difficulty/?country=us&input=${encodeURIComponent(input)}`;
                
                case 'backlinks':
                    const normalizedDomain = normalizeDomain(input);
                    return `https://ahrefs.com/backlink-checker/?input=${encodeURIComponent(normalizedDomain)}&mode=subdomains`;
                
                default:
                    return '';
            }
        }

        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }

        function testAhrefsSearch(type, inputElement) {
            const inputText = inputElement.value.trim();
            if (!inputText) {
                const queryName = type === 'kd' ? 'KD查询' : 'Back Links查询';
                showStatus(`请输入${queryName}内容`, 'error');
                return;
            }
            
            const searchUrl = buildAhrefsUrl(type, inputText);
            
            if (searchUrl) {
                document.getElementById('urlDisplay').innerHTML = `<strong>生成的URL:</strong><br>${searchUrl}`;
                showStatus(`${type === 'kd' ? 'KD' : 'Back Links'} 查询URL已生成`, 'success');
                
                // 在新窗口中打开URL
                window.open(searchUrl, '_blank');
            }
        }

        // 运行测试用例
        function runTests() {
            // KD测试
            document.getElementById('kd-test1').textContent = buildAhrefsUrl('kd', 'ai');
            document.getElementById('kd-test2').textContent = buildAhrefsUrl('kd', 'ai image');
            
            // Back Links测试
            document.getElementById('bl-test1').textContent = buildAhrefsUrl('backlinks', 'https://www.crazygames.com');
            document.getElementById('bl-test2').textContent = buildAhrefsUrl('backlinks', 'www.crazygames.com');
            document.getElementById('bl-test3').textContent = buildAhrefsUrl('backlinks', 'crazygames.com');
            
            // 验证结果
            checkResults();
        }

        function checkResults() {
            const tests = [
                { id: 'kd-test1', expected: 'https://ahrefs.com/keyword-difficulty/?country=us&input=ai' },
                { id: 'kd-test2', expected: 'https://ahrefs.com/keyword-difficulty/?country=us&input=ai%20image' },
                { id: 'bl-test1', expected: 'https://ahrefs.com/backlink-checker/?input=crazygames.com&mode=subdomains' },
                { id: 'bl-test2', expected: 'https://ahrefs.com/backlink-checker/?input=crazygames.com&mode=subdomains' },
                { id: 'bl-test3', expected: 'https://ahrefs.com/backlink-checker/?input=crazygames.com&mode=subdomains' }
            ];

            tests.forEach(test => {
                const element = document.getElementById(test.id);
                const actual = element.textContent;
                if (actual === test.expected) {
                    element.className = 'url-output correct';
                    element.title = '✅ 正确';
                } else {
                    element.className = 'url-output actual';
                    element.title = '❌ 不匹配期望结果';
                }
            });
        }

        // 添加事件监听器
        document.getElementById('kdSearchBtn').addEventListener('click', function() {
            testAhrefsSearch('kd', document.getElementById('kdSearchInput'));
        });

        document.getElementById('backlinksSearchBtn').addEventListener('click', function() {
            testAhrefsSearch('backlinks', document.getElementById('backlinksSearchInput'));
        });

        // 页面加载后运行测试
        window.addEventListener('load', runTests);
    </script>
</body>
</html>
