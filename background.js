// 监听扩展图标点击事件
chrome.action.onClicked.addListener(async (tab) => {
  // 打开侧边栏
  await chrome.sidePanel.open({ tabId: tab.id });
});

// 监听扩展安装事件
chrome.runtime.onInstalled.addListener(() => {
  console.log('链接批量打开器已安装');

  // 设置侧边栏为持久性
  chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true });

  // 初始化右键菜单状态
  chrome.storage.session.get(['enableRightClick'], function(result) {
    if (result.enableRightClick) {
      createContextMenu();
    }
  });
});

// 监听标签页更新，确保侧边栏在新标签页中也可用
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    // 确保侧边栏在所有标签页中都可用
    chrome.sidePanel.setOptions({
      tabId: tabId,
      path: 'popup.html',
      enabled: true
    });
  }
});

// 创建右键菜单
function createContextMenu() {
  chrome.contextMenus.create({
    id: "addToTextarea",
    title: "添加到文本处理器",
    contexts: ["selection"]
  });
}

// 删除右键菜单
function removeContextMenu() {
  chrome.contextMenus.remove("addToTextarea", () => {
    // 忽略错误，菜单可能不存在
    chrome.runtime.lastError;
  });
}

// 监听右键菜单点击事件
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === "addToTextarea" && info.selectionText) {
    // 将选中的文本保存到storage
    chrome.storage.session.set({
      selectedText: info.selectionText,
      timestamp: Date.now()
    });
  }
});

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "enableRightClick") {
    createContextMenu();
    sendResponse({success: true});
  } else if (request.action === "disableRightClick") {
    removeContextMenu();
    sendResponse({success: true});
  }
});
