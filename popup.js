document.addEventListener('DOMContentLoaded', function() {
    const urlInput = document.getElementById('urlInput');
    const openAllBtn = document.getElementById('openAllBtn');
    const clearBtn = document.getElementById('clearBtn');
    const status = document.getElementById('status');

    // 文本处理相关元素
    const textInput = document.getElementById('textInput');
    const processTextBtn = document.getElementById('processTextBtn');
    const clearTextBtn = document.getElementById('clearTextBtn');
    const searchAllBtn = document.getElementById('searchAllBtn');
    const closeTrendsBtn = document.getElementById('closeTrendsBtn');
    const processedResult = document.getElementById('processedResult');
    const resultContent = document.getElementById('resultContent');

    // 关闭已打开链接相关元素
    const closeOpenedBtn = document.getElementById('closeOpenedBtn');

    // 对比词下拉框
    const compareWordSelect = document.getElementById('compareWordSelect');

    // 日期范围选择器
    const dateRangeSelect = document.getElementById('dateRangeSelect');

    // 右键添加文本开关
    const enableRightClickCheckbox = document.getElementById('enableRightClick');

    // 控制数量相关元素
    const enableQuantityControlCheckbox = document.getElementById('enableQuantityControl');
    const progressContainer = document.getElementById('progressContainer');
    const progressDisplay = document.getElementById('progressDisplay');

    // Root Analysis相关元素
    const rootCompareWordSelect = document.getElementById('rootCompareWordSelect');
    const rootDateRangeSelect = document.getElementById('rootDateRangeSelect');
    const startIndexInput = document.getElementById('startIndexInput');
    const countInput = document.getElementById('countInput');
    const openRootLinksBtn = document.getElementById('openRootLinksBtn');
    const closeRootTrendsBtn = document.getElementById('closeRootTrendsBtn');
    const rootWordsList = document.getElementById('rootWordsList');
    const editRootWordsBtn = document.getElementById('editRootWordsBtn');

    // 编辑模态框相关元素
    const editRootWordsModal = document.getElementById('editRootWordsModal');
    const editRootWordsTextarea = document.getElementById('editRootWordsTextarea');
    const saveRootWordsBtn = document.getElementById('saveRootWordsBtn');
    const cancelEditBtn = document.getElementById('cancelEditBtn');
    const resetToDefaultBtn = document.getElementById('resetToDefaultBtn');
    const modalClose = document.querySelector('.modal-close');

    // Google搜索功能相关元素
    const googleSearchInput = document.getElementById('googleSearchInput');
    const addQuotesCheckbox = document.getElementById('addQuotesCheckbox');
    const siteSearchBtn = document.getElementById('siteSearchBtn');
    const intitleSearchBtn = document.getElementById('intitleSearchBtn');
    const allintitleSearchBtn = document.getElementById('allintitleSearchBtn');

    // Ahrefs查询功能相关元素
    const kdSearchInput = document.getElementById('kdSearchInput');
    const kdSearchBtn = document.getElementById('kdSearchBtn');
    const backlinksSearchInput = document.getElementById('backlinksSearchInput');
    const backlinksSearchBtn = document.getElementById('backlinksSearchBtn');

    // 进度控制状态变量
    let currentIndex = 0;  // 当前处理到的词条序号（1基索引）
    let totalCount = 0;    // 总词条数量
    let processedWords = []; // 处理后的词条数组

    // 默认的Root Keywords
    const DEFAULT_ROOT_KEYWORDS = [
        "Translator", "Processor", "Designer", "Compiler", "Analyzer", "Evaluator",
        "Sender", "Receiver", "Interpreter", "Uploader", "Calculator", "Generator",
        "Sample", "Template", "Format", "Builder", "Scheme", "Pattern",
        "Checker", "Detector", "Scraper", "Manager", "Example", "Explorer",
        "Dashboard", "Planner", "Tracker", "Recorder", "Optimizer", "Scheduler",
        "Converter", "Viewer", "Extractor", "Convert", "Monitor", "Notifier",
        "Verifier", "Simulator", "Assistant", "Constructor", "Comparator", "Navigator",
        "Syncer", "Connector", "Online", "Cataloger", "Responder", "Downloader",
        "Maker", "Creator", "Editor", "Guide"
    ];

    // 标签页相关元素
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    // 标签页切换功能
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // 移除所有活动状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // 添加当前活动状态
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');

            // 保存当前选中的标签页
            chrome.storage.session.set({activeTab: targetTab});
        });
    });

    // 进度管理函数
    function updateProgressDisplay() {
        const displayIndex = Math.min(currentIndex, totalCount);
        progressDisplay.textContent = `${displayIndex}/${totalCount}`;
    }

    function resetProgress() {
        currentIndex = 0;
        totalCount = 0;
        processedWords = [];
        updateProgressDisplay();
        progressContainer.classList.add('progress-hidden');
    }

    function showProgress() {
        if (enableQuantityControlCheckbox.checked) {
            progressContainer.classList.remove('progress-hidden');
        }
    }

    function hideProgress() {
        progressContainer.classList.add('progress-hidden');
    }

    // 控制数量checkbox事件监听
    enableQuantityControlCheckbox.addEventListener('change', function() {
        if (this.checked) {
            showProgress();
            // 保存状态
            chrome.storage.session.set({enableQuantityControl: true});
        } else {
            hideProgress();
            // 保存状态
            chrome.storage.session.set({enableQuantityControl: false});
        }
    });

    // 初始化Root Analysis数据
    async function initializeRootAnalysisData() {
        try {
            // 检查是否已经初始化过
            const result = await chrome.storage.local.get(['customRootKeyWords']);

            if (!result.customRootKeyWords) {
                // 首次运行，使用默认词汇
                await chrome.storage.local.set({
                    customRootKeyWords: DEFAULT_ROOT_KEYWORDS
                });

                console.log('Root Analysis数据已初始化:', DEFAULT_ROOT_KEYWORDS.length, '个词汇');
            }
        } catch (error) {
            console.error('初始化Root Analysis数据失败:', error);
        }
    }

    // 显示Root Analysis词汇列表
    async function displayRootWordsList() {
        try {
            const result = await chrome.storage.local.get(['customRootKeyWords']);
            const words = result.customRootKeyWords || [];

            // 按每行5个词分组
            const groupedWords = [];
            for (let i = 0; i < words.length; i += 5) {
                groupedWords.push(words.slice(i, i + 5));
            }

            // 获取已打开的行状态
            const sessionResult = await chrome.storage.session.get(['openedRootAnalysisRows']);
            const openedRows = sessionResult.openedRootAnalysisRows || [];

            // 生成HTML
            let html = '';
            groupedWords.forEach((rowWords, index) => {
                const isOpened = openedRows.includes(index);
                const rowClass = isOpened ? 'root-word-row opened' : 'root-word-row';

                html += `<div class="${rowClass}" data-row-index="${index}">`;
                html += `<span class="root-word-index">${index}.</span>`;

                rowWords.forEach(word => {
                    html += `<span class="root-word-item" data-word="${word}">${word}</span>`;
                    if (word !== rowWords[rowWords.length - 1]) {
                        html += '<span>,</span>';
                    }
                });

                html += '</div>';
            });

            rootWordsList.innerHTML = html;

            // 为每个词添加点击事件
            const wordItems = rootWordsList.querySelectorAll('.root-word-item');
            wordItems.forEach(item => {
                item.addEventListener('click', function() {
                    const word = this.dataset.word;
                    openSingleWordTrends(word);
                });
            });

        } catch (error) {
            console.error('显示词汇列表失败:', error);
        }
    }

    // 打开单个词汇的Google Trends
    function openSingleWordTrends(word) {
        try {
            const compareWord = processSelectedKeyword(rootCompareWordSelect.value);
            const dateRange = rootDateRangeSelect.value;
            const url = buildGoogleTrendsUrl(word, compareWord, dateRange);
            chrome.tabs.create({ url: url });
            showStatus(`已打开 ${word} 的Google Trends`, 'success');
        } catch (error) {
            console.error('打开单词趋势失败:', error);
            showStatus('打开Google Trends失败', 'error');
        }
    }

    // 批量打开Root Analysis链接
    async function openRootAnalysisLinks() {
        try {
            const startIndex = parseInt(startIndexInput.value) || 0;
            const count = parseInt(countInput.value) || 1;

            // 获取词汇数据
            const result = await chrome.storage.local.get(['customRootKeyWords']);
            const words = result.customRootKeyWords || [];

            // 按每行5个词分组
            const groupedWords = [];
            for (let i = 0; i < words.length; i += 5) {
                groupedWords.push(words.slice(i, i + 5));
            }

            // 验证输入
            if (groupedWords.length === 0) {
                showStatus('没有可用的词汇', 'error');
                return;
            }

            if (startIndex < 0) {
                showStatus('起始序号不能小于0', 'error');
                return;
            }

            if (startIndex >= groupedWords.length) {
                showStatus(`起始序号超出范围，最大值为 ${groupedWords.length - 1}`, 'error');
                return;
            }

            if (count <= 0) {
                showStatus('数量必须大于0', 'error');
                return;
            }

            if (startIndex + count > groupedWords.length) {
                showStatus(`数量超出可用范围，最多可打开 ${groupedWords.length - startIndex} 行`, 'error');
                return;
            }

            const compareWord = processSelectedKeyword(rootCompareWordSelect.value);
            const dateRange = rootDateRangeSelect.value;

            let totalOpened = 0;
            const openedRows = [];

            // 批量打开链接
            for (let i = startIndex; i < startIndex + count; i++) {
                const rowWords = groupedWords[i];
                openedRows.push(i);

                for (const word of rowWords) {
                    const url = buildGoogleTrendsUrl(word, compareWord, dateRange);
                    chrome.tabs.create({ url: url });
                    totalOpened++;

                    // 添加延迟避免浏览器阻止
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }

            // 保存打开状态
            chrome.storage.session.set({ openedRootAnalysisRows: openedRows });

            // 更新显示
            displayRootWordsList();

            showStatus(`成功打开 ${totalOpened} 个Google Trends链接`, 'success');

        } catch (error) {
            console.error('批量打开链接失败:', error);
            showStatus('批量打开链接失败', 'error');
        }
    }

    // 关闭所有Google Trends标签页
    async function closeAllGoogleTrends() {
        try {
            showStatus('正在查找Google Trends标签页...', 'info');

            // 多次查询确保获取所有标签页
            let allTrendsTabs = [];
            let attempts = 0;
            const maxAttempts = 3;

            while (attempts < maxAttempts) {
                try {
                    const allTabs = await chrome.tabs.query({});
                    const trendsTabs = allTabs.filter(tab => {
                        if (!tab.url) return false;

                        // 更全面的URL匹配
                        return tab.url.includes('trends.google.com') ||
                               tab.url.includes('trends.google.') ||
                               (tab.url.includes('google.com') && tab.url.includes('trends'));
                    });

                    // 合并结果，去重
                    const existingIds = new Set(allTrendsTabs.map(t => t.id));
                    const newTabs = trendsTabs.filter(t => !existingIds.has(t.id));
                    allTrendsTabs = [...allTrendsTabs, ...newTabs];

                    if (trendsTabs.length === 0 && attempts > 0) {
                        break; // 没有新的标签页了
                    }

                    attempts++;
                    if (attempts < maxAttempts) {
                        await new Promise(resolve => setTimeout(resolve, 100)); // 短暂延迟
                    }
                } catch (queryError) {
                    console.error(`查询标签页失败 (尝试 ${attempts + 1}):`, queryError);
                    attempts++;
                }
            }

            if (allTrendsTabs.length === 0) {
                showStatus('没有找到Google Trends标签页', 'info');
                return;
            }

            // 用户确认
            const confirmMessage = `确定要关闭 ${allTrendsTabs.length} 个Google Trends标签页吗？`;
            if (!confirm(confirmMessage)) {
                return;
            }

            showStatus(`正在关闭 ${allTrendsTabs.length} 个标签页...`, 'info');

            let closedCount = 0;
            let failedCount = 0;
            const failedTabs = [];

            // 分批关闭标签页，避免一次性操作过多
            const batchSize = 5;
            for (let i = 0; i < allTrendsTabs.length; i += batchSize) {
                const batch = allTrendsTabs.slice(i, i + batchSize);

                // 并行关闭当前批次
                const closePromises = batch.map(async (tab) => {
                    try {
                        // 先检查标签页是否仍然存在
                        const currentTab = await chrome.tabs.get(tab.id).catch(() => null);
                        if (!currentTab) {
                            return { success: true, reason: 'already_closed' };
                        }

                        await chrome.tabs.remove(tab.id);
                        return { success: true, tab };
                    } catch (error) {
                        console.error(`关闭标签页失败:`, tab.url, error);
                        return { success: false, tab, error };
                    }
                });

                const results = await Promise.allSettled(closePromises);

                results.forEach((result, index) => {
                    if (result.status === 'fulfilled') {
                        if (result.value.success) {
                            closedCount++;
                        } else {
                            failedCount++;
                            failedTabs.push(result.value.tab);
                        }
                    } else {
                        failedCount++;
                        failedTabs.push(batch[index]);
                    }
                });

                // 批次间短暂延迟
                if (i + batchSize < allTrendsTabs.length) {
                    await new Promise(resolve => setTimeout(resolve, 200));
                }
            }

            // 如果有失败的标签页，尝试重新关闭
            if (failedTabs.length > 0) {
                showStatus(`重试关闭 ${failedTabs.length} 个标签页...`, 'info');

                for (const tab of failedTabs) {
                    try {
                        // 再次检查标签页是否存在
                        const currentTab = await chrome.tabs.get(tab.id).catch(() => null);
                        if (currentTab) {
                            await chrome.tabs.remove(tab.id);
                            closedCount++;
                            failedCount--;
                        } else {
                            failedCount--; // 标签页已经不存在了
                        }
                    } catch (retryError) {
                        console.error(`重试关闭标签页失败:`, tab.url, retryError);
                    }

                    // 重试间延迟
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }

            // 清除Root Analysis的打开状态
            await chrome.storage.session.remove(['openedRootAnalysisRows']);

            // 刷新显示，移除浅蓝色背景
            displayRootWordsList();

            // 显示详细结果
            if (closedCount > 0) {
                if (failedCount > 0) {
                    showStatus(`成功关闭 ${closedCount} 个标签页，${failedCount} 个关闭失败`, 'warning');
                } else {
                    showStatus(`成功关闭所有 ${closedCount} 个Google Trends标签页`, 'success');
                }
            } else {
                showStatus('没有成功关闭任何标签页', 'error');
            }

        } catch (error) {
            console.error('关闭Google Trends标签页时发生错误:', error);
            showStatus('关闭标签页时发生错误: ' + error.message, 'error');
        }
    }

    // 显示编辑模态框
    async function showEditModal() {
        try {
            const result = await chrome.storage.local.get(['customRootKeyWords']);
            const words = result.customRootKeyWords || [];
            editRootWordsTextarea.value = words.join('\n');
            editRootWordsModal.style.display = 'block';

            // 清除之前的错误样式
            editRootWordsTextarea.classList.remove('error');
        } catch (error) {
            console.error('显示编辑模态框失败:', error);
        }
    }

    // 隐藏编辑模态框
    function hideEditModal() {
        editRootWordsModal.style.display = 'none';
    }

    // 保存编辑的词汇
    async function saveEditedWords() {
        try {
            const text = editRootWordsTextarea.value.trim();
            if (!text) {
                showStatus('词汇列表不能为空', 'error');
                return;
            }

            const words = text.split('\n')
                .map(word => word.trim())
                .filter(word => word.length > 0);

            if (words.length === 0) {
                showStatus('没有有效的词汇', 'error');
                return;
            }

            // 检查重复词汇
            const duplicateCheck = checkDuplicateWords(words);
            if (duplicateCheck.hasDuplicates) {
                const duplicateWords = duplicateCheck.duplicates.join(', ');
                showStatus(`存在重复的词汇: ${duplicateWords}，请删除重复项后再保存`, 'error');

                // 添加错误样式并高亮显示重复的词汇
                editRootWordsTextarea.classList.add('error');
                highlightDuplicatesInTextarea(words, duplicateCheck.duplicates);

                // 3秒后移除错误样式
                setTimeout(() => {
                    editRootWordsTextarea.classList.remove('error');
                }, 3000);

                return;
            }

            await chrome.storage.local.set({ customRootKeyWords: words });
            hideEditModal();
            displayRootWordsList();
            showStatus(`已保存 ${words.length} 个词汇`, 'success');

        } catch (error) {
            console.error('保存词汇失败:', error);
            showStatus('保存失败', 'error');
        }
    }

    // 检查重复词汇
    function checkDuplicateWords(words) {
        const wordCount = {};
        const duplicates = [];

        // 统计每个词汇出现的次数（不区分大小写）
        words.forEach(word => {
            const lowerWord = word.toLowerCase();
            wordCount[lowerWord] = (wordCount[lowerWord] || 0) + 1;
        });

        // 找出重复的词汇
        Object.keys(wordCount).forEach(lowerWord => {
            if (wordCount[lowerWord] > 1) {
                // 找到原始大小写的词汇
                const originalWord = words.find(word => word.toLowerCase() === lowerWord);
                duplicates.push(originalWord);
            }
        });

        return {
            hasDuplicates: duplicates.length > 0,
            duplicates: duplicates
        };
    }

    // 在文本框中高亮显示重复的词汇
    function highlightDuplicatesInTextarea(allWords, duplicateWords) {
        const textarea = editRootWordsTextarea;
        const text = textarea.value;

        // 找到第一个重复词汇的位置并选中
        if (duplicateWords.length > 0) {
            const firstDuplicate = duplicateWords[0];
            const lines = text.split('\n');
            let charPosition = 0;

            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();
                if (line.toLowerCase() === firstDuplicate.toLowerCase()) {
                    // 找到重复词汇，选中这一行
                    const lineStart = charPosition;
                    const lineEnd = charPosition + lines[i].length;

                    textarea.focus();
                    textarea.setSelectionRange(lineStart, lineEnd);
                    break;
                }
                charPosition += lines[i].length + 1; // +1 for the newline character
            }
        }
    }

    // 恢复默认词汇
    async function resetToDefaultWords() {
        try {
            editRootWordsTextarea.value = DEFAULT_ROOT_KEYWORDS.join('\n');
            showStatus('已恢复到默认词汇', 'info');
        } catch (error) {
            console.error('恢复默认词汇失败:', error);
            showStatus('恢复默认失败', 'error');
        }
    }

    // 统一的状态恢复函数
    function restoreCompleteState() {
        chrome.storage.session.get([
            'savedUrls',
            'savedText',
            'savedCompareWord',
            'savedDateRange',
            'processedWords',
            'showProcessedResult',
            'activeTab',
            'enableRightClick',
            'enableQuantityControl',
            'currentIndex',
            'totalCount',
            'rootCompareWord',
            'rootDateRange',
            'rootStartIndex',
            'rootCount',
            'savedGoogleSearchText',
            'savedAddQuotes',
            'savedKdSearchText',
            'savedBacklinksSearchText'
        ], function(result) {
            // 恢复链接输入内容
            if (result.savedUrls) {
                urlInput.value = result.savedUrls;
            }

            // 恢复文本输入内容
            if (result.savedText) {
                textInput.value = result.savedText;
            }

            // 恢复对比词选择
            if (result.savedCompareWord !== undefined) {
                compareWordSelect.value = result.savedCompareWord;
            }

            // 恢复日期范围选择
            if (result.savedDateRange !== undefined) {
                dateRangeSelect.value = result.savedDateRange;
            }

            // 恢复右键开关状态
            if (result.enableRightClick !== undefined) {
                enableRightClickCheckbox.checked = result.enableRightClick;
            }

            // 恢复控制数量开关状态
            if (result.enableQuantityControl !== undefined) {
                enableQuantityControlCheckbox.checked = result.enableQuantityControl;
            }

            // 恢复Google搜索输入框内容
            if (result.savedGoogleSearchText !== undefined) {
                googleSearchInput.value = result.savedGoogleSearchText;
            }

            // 恢复Google搜索引号checkbox状态
            if (result.savedAddQuotes !== undefined) {
                addQuotesCheckbox.checked = result.savedAddQuotes;
            }

            // 恢复Ahrefs查询输入框内容
            if (result.savedKdSearchText !== undefined) {
                kdSearchInput.value = result.savedKdSearchText;
            }

            if (result.savedBacklinksSearchText !== undefined) {
                backlinksSearchInput.value = result.savedBacklinksSearchText;
            }

            // 恢复进度状态
            if (result.currentIndex !== undefined && result.totalCount !== undefined) {
                currentIndex = result.currentIndex;
                totalCount = result.totalCount;
                updateProgressDisplay();
                if (enableQuantityControlCheckbox.checked) {
                    showProgress();
                }
            }

            // 恢复Root Analysis状态
            if (result.rootCompareWord !== undefined) {
                rootCompareWordSelect.value = result.rootCompareWord;
            }
            if (result.rootDateRange !== undefined) {
                rootDateRangeSelect.value = result.rootDateRange;
            }
            if (result.rootStartIndex !== undefined) {
                startIndexInput.value = result.rootStartIndex;
            } else {
                startIndexInput.value = '0'; // 默认值
            }
            if (result.rootCount !== undefined) {
                countInput.value = result.rootCount;
            } else {
                countInput.value = '1'; // 默认值
            }

            // 初始化Root Analysis
            initializeRootAnalysisData().then(() => {
                displayRootWordsList();
            });

            // 恢复当前选中的标签页
            if (result.activeTab) {
                // 移除所有活动状态
                tabBtns.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));

                // 恢复选中的标签
                const activeTabBtn = document.querySelector(`[data-tab="${result.activeTab}"]`);
                const activeTabContent = document.getElementById(result.activeTab);
                if (activeTabBtn && activeTabContent) {
                    activeTabBtn.classList.add('active');
                    activeTabContent.classList.add('active');
                }
            }

            // 恢复处理文本的结果
            if (result.processedWords && result.processedWords.length > 0) {
                processedWords = result.processedWords; // 同步更新全局变量
                renderWordList(result.processedWords);
                if (result.showProcessedResult) {
                    processedResult.classList.add('show');
                    adjustResultHeight();
                }
            }

            // 恢复关闭按钮状态
            updateCloseButtonVisibility();
        });
    }

    // 更新关闭按钮的显示状态
    function updateCloseButtonVisibility() {
        chrome.storage.session.get(['openedTabIds', 'openedUrls'], async function(result) {
            if (result.openedTabIds && result.openedTabIds.length > 0) {
                // 验证标签页是否仍然存在
                let validTabCount = 0;
                const validTabIds = [];

                try {
                    for (const tabId of result.openedTabIds) {
                        try {
                            await chrome.tabs.get(tabId);
                            validTabIds.push(tabId);
                            validTabCount++;
                        } catch (error) {
                            // 标签页不存在，忽略
                        }
                    }

                    if (validTabCount > 0) {
                        closeOpenedBtn.style.display = 'inline-block';
                        closeOpenedBtn.textContent = `关闭已打开链接 (${validTabCount})`;

                        // 更新存储中的有效标签页ID
                        if (validTabIds.length !== result.openedTabIds.length) {
                            chrome.storage.session.set({openedTabIds: validTabIds});
                        }
                    } else {
                        // 所有标签页都已被关闭，清理存储并隐藏按钮
                        closeOpenedBtn.style.display = 'none';
                        chrome.storage.session.remove(['openedTabIds', 'openedUrls', 'openedTimestamp']);
                    }
                } catch (error) {
                    console.error('验证标签页状态时发生错误:', error);
                    closeOpenedBtn.style.display = 'none';
                }
            } else {
                closeOpenedBtn.style.display = 'none';
            }
        });
    }

    // 初始加载状态
    restoreCompleteState();

    // 多重状态恢复机制 - 确保在各种情况下都能恢复状态

    // 1. 页面可见性变化时恢复状态（标签切换）
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            restoreCompleteState();
        }
    });

    // 2. 窗口获得焦点时恢复状态
    window.addEventListener('focus', function() {
        restoreCompleteState();
    });

    // 3. 定期检查状态一致性（每5秒检查一次）
    setInterval(function() {
        if (!document.hidden) {
            restoreCompleteState();
            // 额外检查关闭按钮状态
            updateCloseButtonVisibility();
        }
    }, 5000);

    // 打开全部链接
    openAllBtn.addEventListener('click', async function() {
        const inputText = urlInput.value.trim();

        if (!inputText) {
            showStatus('请输入链接地址', 'error');
            return;
        }

        // 解析URL
        const parsedUrls = parseUrls(inputText);

        if (parsedUrls.length === 0) {
            showStatus('未找到有效的链接地址', 'error');
            return;
        }

        // 对链接进行去重处理
        const deduplicatedUrls = deduplicateUrls(parsedUrls);

        const originalCount = parsedUrls.length;
        const finalCount = deduplicatedUrls.length;

        if (originalCount !== finalCount) {
            showStatus(`去重处理：${originalCount} 个链接去重后剩余 ${finalCount} 个，正在打开...`, 'info');
        } else {
            showStatus(`正在打开 ${finalCount} 个链接...`, 'info');
        }

        // 批量打开去重后的链接
        let openedCount = 0;
        const openedTabIds = [];
        const openedUrls = [];

        for (const url of deduplicatedUrls) {
            try {
                // 添加延迟避免被浏览器阻止
                await new Promise(resolve => setTimeout(resolve, 100));
                const tab = await chrome.tabs.create({ url: url });
                openedTabIds.push(tab.id);
                openedUrls.push(url);
                openedCount++;
            } catch (error) {
                console.error('打开链接失败:', url, error);
            }
        }

        // 保存已打开的标签页信息
        if (openedTabIds.length > 0) {
            chrome.storage.session.set({
                openedTabIds: openedTabIds,
                openedUrls: openedUrls,
                openedTimestamp: Date.now()
            });

            // 显示关闭按钮
            updateCloseButtonVisibility();
        }

        showStatus(`成功打开 ${openedCount} 个链接`, 'success');
    });

    // 清理内容
    clearBtn.addEventListener('click', function() {
        urlInput.value = '';
        chrome.storage.session.remove(['savedUrls']);
        showStatus('内容已清理', 'info');

        // 聚焦到输入框
        urlInput.focus();
    });

    // 关闭已打开的链接
    closeOpenedBtn.addEventListener('click', async function() {
        try {
            const result = await chrome.storage.session.get(['openedTabIds', 'openedUrls']);

            if (!result.openedTabIds || result.openedTabIds.length === 0) {
                showStatus('没有需要关闭的标签页', 'info');
                updateCloseButtonVisibility();
                return;
            }

            // 用户确认
            const confirmMessage = `确定要关闭 ${result.openedTabIds.length} 个已打开的标签页吗？`;
            if (!confirm(confirmMessage)) {
                return;
            }

            let closedCount = 0;
            const failedUrls = [];

            // 方法1：通过标签页ID直接关闭（精确快速）
            for (const tabId of result.openedTabIds) {
                try {
                    await chrome.tabs.remove(tabId);
                    closedCount++;
                } catch (error) {
                    console.log(`标签页 ${tabId} 关闭失败，可能已被手动关闭:`, error);
                    // 记录失败的标签页，稍后通过URL匹配尝试关闭
                }
            }

            // 方法2：通过URL匹配关闭（兜底方案）
            if (result.openedUrls && result.openedUrls.length > 0 && closedCount < result.openedTabIds.length) {
                try {
                    const allTabs = await chrome.tabs.query({});
                    const tabsToClose = allTabs.filter(tab =>
                        result.openedUrls.includes(tab.url) &&
                        !result.openedTabIds.includes(tab.id) // 避免重复关闭
                    );

                    if (tabsToClose.length > 0) {
                        for (const tab of tabsToClose) {
                            try {
                                await chrome.tabs.remove(tab.id);
                                closedCount++;
                            } catch (error) {
                                console.log(`通过URL匹配关闭标签页失败:`, tab.url, error);
                                failedUrls.push(tab.url);
                            }
                        }
                    }
                } catch (error) {
                    console.error('查询标签页失败:', error);
                }
            }

            // 清除存储的标签页信息
            chrome.storage.session.remove(['openedTabIds', 'openedUrls', 'openedTimestamp']);

            // 隐藏关闭按钮
            updateCloseButtonVisibility();

            // 显示结果
            if (closedCount > 0) {
                showStatus(`成功关闭 ${closedCount} 个标签页`, 'success');
            } else {
                showStatus('没有找到需要关闭的标签页', 'info');
            }

        } catch (error) {
            console.error('关闭标签页时发生错误:', error);
            showStatus('关闭标签页时发生错误', 'error');
        }
    });

    // 链接去重函数
    function deduplicateUrls(urls) {
        // 第一步：基础去重 - 去除完全相同的链接
        const uniqueUrls = [...new Set(urls)];

        // 第二步：智能去重 - 使用冒泡比较方式处理相似链接
        const result = [];
        const toRemove = new Set(); // 记录需要移除的URL索引

        // 冒泡比较每一对URL
        for (let i = 0; i < uniqueUrls.length; i++) {
            if (toRemove.has(i)) continue; // 已标记删除的跳过

            for (let j = i + 1; j < uniqueUrls.length; j++) {
                if (toRemove.has(j)) continue; // 已标记删除的跳过

                if (isSimilarUrl(uniqueUrls[i], uniqueUrls[j])) {
                    // 发现相似URL，决定保留哪一个
                    const keepIndex = chooseBetterUrl(uniqueUrls[i], uniqueUrls[j]) === uniqueUrls[i] ? i : j;
                    const removeIndex = keepIndex === i ? j : i;
                    toRemove.add(removeIndex);
                }
            }
        }

        // 构建最终结果，排除被标记删除的URL
        for (let i = 0; i < uniqueUrls.length; i++) {
            if (!toRemove.has(i)) {
                result.push(uniqueUrls[i]);
            }
        }

        return result;
    }

    // 判断两个URL是否相似（同一页面的不同语言版本）
    function isSimilarUrl(url1, url2) {
        try {
            const u1 = new URL(url1);
            const u2 = new URL(url2);

            // 1. 主域名必须相同
            if (u1.hostname !== u2.hostname) {
                return false;
            }

            // 2. 提取并比较路径（去除语言代码后）
            const path1 = removeLanguageCode(u1.pathname);
            const path2 = removeLanguageCode(u2.pathname);

            // 3. 去除语言代码后路径相同，且原路径不同（说明只是语言版本差异）
            return path1 === path2 && u1.pathname !== u2.pathname;
        } catch (error) {
            // URL解析失败，认为不相似
            return false;
        }
    }

    // 移除路径中的语言代码
    function removeLanguageCode(pathname) {
        // 移除常见的语言代码：/en/, /zh/, /fr/, /de/, /dk/, /tr/, /es/, /it/, /pt/, /ru/, /ja/, /ko/, /nl/, /sv/, /no/ 等
        return pathname.replace(/\/(en|zh|fr|de|dk|tr|es|it|pt|ru|ja|ko|nl|sv|no|da|fi|pl|cs|hu|ro|bg|hr|sk|sl|et|lv|lt|mt|cy|ga|eu|ca|gl|br|oc|co|sc|rm|lb|is|fo|kl|se|sma|smj|smn|sms|fkv|fit|se|sma|smj|smn|sms)\//gi, '/');
    }

    // 在两个相似URL中选择更好的一个
    function chooseBetterUrl(url1, url2) {
        try {
            const u1 = new URL(url1);
            const u2 = new URL(url2);

            // 优先选择包含 "/en/" 的英文版本
            const url1HasEn = u1.pathname.includes('/en/');
            const url2HasEn = u2.pathname.includes('/en/');

            if (url1HasEn && !url2HasEn) {
                return url1;
            } else if (url2HasEn && !url1HasEn) {
                return url2;
            } else {
                // 如果都有或都没有英文版本，保留第一个
                return url1;
            }
        } catch (error) {
            // 解析失败，返回第一个
            return url1;
        }
    }

    // 解析URL函数
    function parseUrls(text) {
        const lines = text.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);

        const urls = [];
        const urlRegex = /^(https?:\/\/)?([\w\-]+\.)+[\w\-]+(\/\S*)?$/i;

        for (const line of lines) {
            let url = line.trim();

            // 如果没有http前缀，添加https://
            if (!url.startsWith('http')) {
                if (url.startsWith('www.')) {
                    url = 'https://' + url;
                } else {
                    url = 'https://' + url;
                }
            }

            // 验证URL格式
            if (urlRegex.test(url)) {
                urls.push(url);
            }
        }

        return urls;
    }

    // 显示状态信息
    function showStatus(message, type) {
        status.textContent = message;
        status.className = 'status ' + type;
        
        // 3秒后自动清除状态信息
        setTimeout(() => {
            status.textContent = '';
            status.className = 'status';
        }, 3000);
    }

    // 监听Ctrl+Enter快捷键
    urlInput.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'Enter') {
            openAllBtn.click();
        }
    });

    // 实时保存链接输入内容
    urlInput.addEventListener('input', function() {
        const inputText = urlInput.value;
        chrome.storage.session.set({savedUrls: inputText});
    });

    // 监听粘贴事件，确保粘贴时也能保存
    urlInput.addEventListener('paste', function() {
        // 使用setTimeout确保粘贴内容已经更新到输入框
        setTimeout(() => {
            const inputText = urlInput.value;
            chrome.storage.session.set({savedUrls: inputText});
        }, 10);
    });

    // 文本处理功能
    processTextBtn.addEventListener('click', function() {
        const inputText = textInput.value.trim();

        if (!inputText) {
            showStatus('请输入要处理的文本', 'error');
            return;
        }

        // 处理文本
        const processedText = processText(inputText);

        if (processedText.length === 0) {
            showStatus('处理后没有有效内容', 'info');
            resultContent.innerHTML = '<div class="empty-result">无有效内容</div>';
            // 重置进度状态
            resetProgress();
            // 保存空结果状态
            chrome.storage.session.set({
                processedWords: [],
                showProcessedResult: true
            });
        } else {
            showStatus(`成功处理 ${processedText.length} 个词条`, 'success');
            renderWordList(processedText);

            // 更新进度状态
            processedWords = processedText;
            totalCount = processedText.length;
            currentIndex = 1; // 处理完成后设置为1
            updateProgressDisplay();
            showProgress(); // 如果启用了控制数量，显示进度

            // 保存处理结果到存储
            chrome.storage.session.set({
                processedWords: processedText,
                showProcessedResult: true,
                currentIndex: currentIndex,
                totalCount: totalCount
            });
        }

        processedResult.classList.add('show');

        // 显示结果后调整高度
        adjustResultHeight();
    });

    // 清理文本
    clearTextBtn.addEventListener('click', function() {
        textInput.value = '';
        compareWordSelect.value = 'Gpts( 5k )'; // 重置为默认值
        dateRangeSelect.value = 'now 7-d'; // 重置为默认值
        enableRightClickCheckbox.checked = false; // 重置右键开关
        enableQuantityControlCheckbox.checked = false; // 重置控制数量开关

        // 重置进度状态
        resetProgress();

        // 重置Root Analysis状态
        rootCompareWordSelect.value = 'Gpts( 5k )';
        rootDateRangeSelect.value = 'now 7-d';
        startIndexInput.value = '0';
        countInput.value = '1';

        chrome.storage.session.remove([
            'savedText', 'savedCompareWord', 'savedDateRange', 'enableRightClick',
            'processedWords', 'showProcessedResult', 'rootCompareWord', 'rootDateRange',
            'rootStartIndex', 'rootCount', 'openedRootAnalysisRows', 'enableQuantityControl',
            'currentIndex', 'totalCount'
        ]);

        // 禁用右键菜单
        chrome.runtime.sendMessage({action: "disableRightClick"});

        // 刷新Root Analysis显示
        displayRootWordsList();
        processedResult.classList.remove('show');
        resultContent.innerHTML = '';
        showStatus('文本内容已清理', 'info');

        // 聚焦到输入框
        textInput.focus();
    });

    // Search All 按钮功能
    searchAllBtn.addEventListener('click', async function() {
        try {
            // 获取当前处理结果中的所有词条
            const wordItems = resultContent.querySelectorAll('.word-item');

            if (wordItems.length === 0) {
                showStatus('搜索结果为空', 'info');
                return;
            }

            const selectedKeyword = compareWordSelect.value;
            const compareWord = processSelectedKeyword(selectedKeyword);
            const dateRange = dateRangeSelect.value;
            const selectedText = dateRangeSelect.options[dateRangeSelect.selectedIndex].text;

            // 检查是否启用控制数量
            if (enableQuantityControlCheckbox.checked) {
                // 启用控制数量模式
                await handleQuantityControlledSearch(wordItems, compareWord, dateRange, selectedText);
            } else {
                // 不启用控制数量，保持原有功能
                await handleNormalSearch(wordItems, compareWord, dateRange, selectedText);
            }

        } catch (error) {
            console.error('批量打开Google Trends失败:', error);
            showStatus('批量打开Google Trends失败', 'error');
        }
    });

    // 不启用控制数量的正常搜索模式
    async function handleNormalSearch(wordItems, compareWord, dateRange, selectedText) {
        let openedCount = 0;
        const totalCount = wordItems.length;

        // 显示开始状态
        showStatus(`正在批量打开 ${totalCount} 个Google Trends页面...`, 'info');

        // 批量打开所有词条的Google Trends页面
        for (const wordItem of wordItems) {
            const wordText = wordItem.querySelector('.word-text').textContent;

            try {
                // 添加延迟避免被浏览器阻止
                await new Promise(resolve => setTimeout(resolve, 100));

                const url = buildGoogleTrendsUrl(wordText.trim(), compareWord, dateRange);
                await chrome.tabs.create({ url: url });
                openedCount++;
            } catch (error) {
                console.error('打开Google Trends失败:', wordText, error);
            }
        }

        // 更新进度显示（如果显示的话）
        if (enableQuantityControlCheckbox.checked) {
            currentIndex = totalCount + 1; // 设置为总数+1，表示所有词条都已处理完
            updateProgressDisplay();
        }

        // 显示完成状态
        if (compareWord && compareWord.trim()) {
            showStatus(`成功打开 ${openedCount} 个Google Trends比较页面 (vs "${compareWord.trim()}", ${selectedText})`, 'success');
        } else {
            showStatus(`成功打开 ${openedCount} 个Google Trends分析页面 (${selectedText})`, 'success');
        }
    }

    // 启用控制数量的分批搜索模式
    async function handleQuantityControlledSearch(wordItems, compareWord, dateRange, selectedText) {
        const totalWords = wordItems.length;

        // 检查是否还有词条可以处理
        if (currentIndex > totalWords) {
            showStatus('没有更多网页可加载', 'info');
            return;
        }

        // 计算本次要处理的词条范围
        // currentIndex是1基索引（显示用），需要转换为0基索引（数组用）
        const startIndex = currentIndex - 1; // 转换为0基索引
        const endIndex = Math.min(startIndex + 10, totalWords);
        const batchSize = endIndex - startIndex;

        // 显示开始状态
        showStatus(`正在打开第 ${currentIndex}-${currentIndex + batchSize - 1} 个Google Trends页面 (共${totalWords}个)...`, 'info');

        let openedCount = 0;

        // 批量打开当前批次的词条
        for (let i = startIndex; i < endIndex; i++) {
            const wordItem = wordItems[i];
            const wordText = wordItem.querySelector('.word-text').textContent;

            try {
                // 添加延迟避免被浏览器阻止
                await new Promise(resolve => setTimeout(resolve, 100));

                const url = buildGoogleTrendsUrl(wordText.trim(), compareWord, dateRange);
                await chrome.tabs.create({ url: url });
                openedCount++;
            } catch (error) {
                console.error('打开Google Trends失败:', wordText, error);
            }
        }

        // 更新进度（转换回1基索引用于显示）并持久化，避免侧边栏重载后回退
        currentIndex = endIndex + 1;
        totalCount = totalWords;
        updateProgressDisplay();
        try {
            await chrome.storage.session.set({ currentIndex, totalCount });
        } catch (e) {
            console.warn('保存进度失败（忽略，不影响继续操作）', e);
        }

        // 显示完成状态
        const displayStart = Math.min(Math.max(currentIndex - openedCount, 1), totalWords);
        const displayEnd = Math.min(currentIndex - 1, totalWords);
        if (compareWord && compareWord.trim()) {
            showStatus(`成功打开 ${openedCount} 个Google Trends比较页面 (vs "${compareWord.trim()}", ${selectedText}) - 进度: ${displayEnd}/${totalWords}（本次 ${displayStart}-${displayEnd}）`, 'success');
        } else {
            showStatus(`成功打开 ${openedCount} 个Google Trends分析页面 (${selectedText}) - 进度: ${displayEnd}/${totalWords}（本次 ${displayStart}-${displayEnd}）`, 'success');
        }
    }

    // 关闭Tabs按钮功能 - 使用改进的关闭逻辑
    closeTrendsBtn.addEventListener('click', async function() {
        await closeAllGoogleTrends();
    });

    // Google搜索按钮事件监听器
    siteSearchBtn.addEventListener('click', function() {
        openGoogleSearch('site');
    });

    intitleSearchBtn.addEventListener('click', function() {
        openGoogleSearch('intitle');
    });

    allintitleSearchBtn.addEventListener('click', function() {
        openGoogleSearch('allintitle');
    });

    // Ahrefs查询按钮事件监听器
    kdSearchBtn.addEventListener('click', function() {
        performAhrefsSearch('kd', kdSearchInput);
    });

    backlinksSearchBtn.addEventListener('click', function() {
        performAhrefsSearch('backlinks', backlinksSearchInput);
    });

    // 文本处理函数
    function processText(text) {
        const lines = text.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);

        const processedLines = [];
        const seenWords = new Set(); // 用于去重

        for (const line of lines) {
            // 检查是否全部是数字（包括小数点）
            if (/^\d+\.?\d*$/.test(line)) {
                // 全部是数字，跳过
                continue;
            }

            // 将"-"替换为空格
            const processed = line.replace(/-/g, ' ');

            // 检查是否重复（不区分大小写进行比较，但保留原始大小写）
            const lowerCaseForComparison = processed.toLowerCase();
            if (!seenWords.has(lowerCaseForComparison)) {
                seenWords.add(lowerCaseForComparison);
                processedLines.push(processed); // 保留原始大小写格式
            }
        }

        return processedLines;
    }

    // 渲染词条列表
    function renderWordList(words) {
        resultContent.innerHTML = '';

        words.forEach((word, index) => {
            const wordItem = document.createElement('div');
            wordItem.className = 'word-item';
            wordItem.setAttribute('data-index', index);

            wordItem.innerHTML = `
                <span class="word-text">${word}</span>
                <div class="word-actions">
                    <button class="action-btn copy-btn" title="复制" data-action="copy" data-word="${word}">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="icon">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z" />
                        </svg>
                    </button>
                    <button class="action-btn trends-btn" title="Google Trends比较" data-action="trends" data-word="${word}">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="icon">
                            <path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
                        </svg>
                    </button>
                    <button class="action-btn delete-btn" title="删除" data-action="delete" data-index="${index}">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="icon">
                            <path stroke-linecap="round" stroke-linejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
                        </svg>
                    </button>
                </div>
            `;

            resultContent.appendChild(wordItem);
        });

        // 添加按钮事件监听
        addWordActionListeners();

        // 动态调整结果区域高度
        adjustResultHeight();
    }

    // 添加词条操作按钮事件监听
    function addWordActionListeners() {
        const actionBtns = resultContent.querySelectorAll('.action-btn');

        actionBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.stopPropagation();
                const action = this.getAttribute('data-action');

                if (action === 'copy') {
                    const word = this.getAttribute('data-word');
                    copyToClipboard(word);
                } else if (action === 'trends') {
                    const word = this.getAttribute('data-word');
                    openGoogleTrends(word);
                } else if (action === 'delete') {
                    const index = parseInt(this.getAttribute('data-index'));
                    deleteWordItem(index);
                }
            });
        });
    }

    // 复制到剪贴板
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            showStatus(`已复制: ${text}`, 'success');
        }).catch(() => {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showStatus(`已复制: ${text}`, 'success');
        });
    }

    // 删除词条
    function deleteWordItem(index) {
        const wordItems = resultContent.querySelectorAll('.word-item');
        if (wordItems[index]) {
            const wordText = wordItems[index].querySelector('.word-text').textContent;
            wordItems[index].remove();
            showStatus(`已删除: ${wordText}`, 'info');

            // 重新编号剩余的词条
            updateWordIndices();

            // 更新存储中的词条列表
            updateStoredWordList();
        }
    }

    // 更新词条索引
    function updateWordIndices() {
        const wordItems = resultContent.querySelectorAll('.word-item');
        wordItems.forEach((item, newIndex) => {
            item.setAttribute('data-index', newIndex);
            const deleteBtn = item.querySelector('.delete-btn');
            deleteBtn.setAttribute('data-index', newIndex);
        });

        // 重新调整高度
        adjustResultHeight();
    }

    // 更新存储中的词条列表
    function updateStoredWordList() {
        const wordItems = resultContent.querySelectorAll('.word-item');
        const currentWords = Array.from(wordItems).map(item =>
            item.querySelector('.word-text').textContent
        );

        // 更新存储
        chrome.storage.session.set({
            processedWords: currentWords,
            showProcessedResult: currentWords.length > 0
        });

        // 如果没有词条了，隐藏结果区域
        if (currentWords.length === 0) {
            processedResult.classList.remove('show');
        }
    }

    // 动态调整结果区域高度
    function adjustResultHeight() {
        if (!processedResult.classList.contains('show')) {
            return;
        }

        // 使用setTimeout确保DOM已经更新
        setTimeout(() => {
            // 获取视窗高度
            const viewportHeight = window.innerHeight;

            // 获取结果内容区域距离顶部的距离
            const contentRect = resultContent.getBoundingClientRect();
            const contentTop = contentRect.top;

            // 计算可用高度（距离底部50px）
            const availableHeight = viewportHeight - contentTop - 50;

            // 设置最小高度为80px，确保至少能显示一个词条
            const finalHeight = Math.max(80, availableHeight);

            // 获取内容的实际高度
            const contentHeight = resultContent.scrollHeight;

            // 如果内容高度小于可用高度，不设置max-height，让内容自然展开
            if (contentHeight <= finalHeight) {
                resultContent.style.maxHeight = 'none';
                resultContent.style.overflowY = 'visible';
            } else {
                // 内容超出可用高度时，设置max-height并显示滚动条
                resultContent.style.maxHeight = finalHeight + 'px';
                resultContent.style.overflowY = 'auto';
            }
        }, 10);
    }

    // Root Analysis事件监听器
    openRootLinksBtn.addEventListener('click', openRootAnalysisLinks);
    closeRootTrendsBtn.addEventListener('click', closeAllGoogleTrends);
    editRootWordsBtn.addEventListener('click', showEditModal);
    saveRootWordsBtn.addEventListener('click', saveEditedWords);
    cancelEditBtn.addEventListener('click', hideEditModal);
    resetToDefaultBtn.addEventListener('click', resetToDefaultWords);
    modalClose.addEventListener('click', hideEditModal);

    // 点击模态框外部关闭
    editRootWordsModal.addEventListener('click', function(e) {
        if (e.target === editRootWordsModal) {
            hideEditModal();
        }
    });

    // Root Analysis下拉框状态保存
    rootCompareWordSelect.addEventListener('change', function() {
        chrome.storage.session.set({rootCompareWord: rootCompareWordSelect.value});
    });

    rootDateRangeSelect.addEventListener('change', function() {
        chrome.storage.session.set({rootDateRange: rootDateRangeSelect.value});
    });

    // Root Analysis输入框状态保存
    startIndexInput.addEventListener('input', function() {
        chrome.storage.session.set({rootStartIndex: startIndexInput.value});
    });

    countInput.addEventListener('input', function() {
        chrome.storage.session.set({rootCount: countInput.value});
    });

    // 编辑文本框实时检查重复
    editRootWordsTextarea.addEventListener('input', function() {
        // 移除错误样式
        editRootWordsTextarea.classList.remove('error');

        // 实时检查重复（可选功能，避免过于频繁的检查）
        clearTimeout(editRootWordsTextarea.checkTimeout);
        editRootWordsTextarea.checkTimeout = setTimeout(() => {
            const text = editRootWordsTextarea.value.trim();
            if (text) {
                const words = text.split('\n')
                    .map(word => word.trim())
                    .filter(word => word.length > 0);

                const duplicateCheck = checkDuplicateWords(words);
                if (duplicateCheck.hasDuplicates) {
                    // 轻微提示，不阻止编辑
                    editRootWordsTextarea.style.borderColor = '#fbbf24';
                } else {
                    editRootWordsTextarea.style.borderColor = '';
                }
            }
        }, 500); // 500ms延迟，避免过于频繁的检查
    });

    // 为文本输入框添加快捷键支持
    textInput.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'Enter') {
            processTextBtn.click();
        }
    });

    // 实时保存文本输入内容
    textInput.addEventListener('input', function() {
        const inputText = textInput.value;
        chrome.storage.session.set({savedText: inputText});
    });

    // 监听粘贴事件，确保粘贴时也能保存
    textInput.addEventListener('paste', function() {
        // 使用setTimeout确保粘贴内容已经更新到输入框
        setTimeout(() => {
            const inputText = textInput.value;
            chrome.storage.session.set({savedText: inputText});
        }, 10);
    });

    // 实时保存对比词选择
    compareWordSelect.addEventListener('change', function() {
        const selectedValue = compareWordSelect.value;
        chrome.storage.session.set({savedCompareWord: selectedValue});
    });

    // 实时保存日期范围选择
    dateRangeSelect.addEventListener('change', function() {
        const selectedValue = dateRangeSelect.value;
        chrome.storage.session.set({savedDateRange: selectedValue});
    });

    // 右键开关事件处理
    enableRightClickCheckbox.addEventListener('change', function() {
        const isEnabled = enableRightClickCheckbox.checked;

        // 保存状态
        chrome.storage.session.set({enableRightClick: isEnabled});

        // 发送消息给background script
        if (isEnabled) {
            chrome.runtime.sendMessage({action: "enableRightClick"});
            showStatus('右键添加文本功能已启用', 'success');
        } else {
            chrome.runtime.sendMessage({action: "disableRightClick"});
            showStatus('右键添加文本功能已禁用', 'info');
        }
    });

    // Google搜索输入框实时保存内容
    googleSearchInput.addEventListener('input', function() {
        const inputText = googleSearchInput.value;
        chrome.storage.session.set({savedGoogleSearchText: inputText});
    });

    // 监听粘贴事件，确保粘贴时也能保存
    googleSearchInput.addEventListener('paste', function() {
        // 使用setTimeout确保粘贴内容已经更新到输入框
        setTimeout(() => {
            const inputText = googleSearchInput.value;
            chrome.storage.session.set({savedGoogleSearchText: inputText});
        }, 10);
    });

    // Google搜索引号checkbox状态保存
    addQuotesCheckbox.addEventListener('change', function() {
        const isChecked = addQuotesCheckbox.checked;
        chrome.storage.session.set({savedAddQuotes: isChecked});
    });

    // Ahrefs查询输入框实时保存内容
    kdSearchInput.addEventListener('input', function() {
        const inputText = kdSearchInput.value;
        chrome.storage.session.set({savedKdSearchText: inputText});
    });

    kdSearchInput.addEventListener('paste', function() {
        setTimeout(() => {
            const inputText = kdSearchInput.value;
            chrome.storage.session.set({savedKdSearchText: inputText});
        }, 10);
    });

    backlinksSearchInput.addEventListener('input', function() {
        const inputText = backlinksSearchInput.value;
        chrome.storage.session.set({savedBacklinksSearchText: inputText});
    });

    backlinksSearchInput.addEventListener('paste', function() {
        setTimeout(() => {
            const inputText = backlinksSearchInput.value;
            chrome.storage.session.set({savedBacklinksSearchText: inputText});
        }, 10);
    });

    // 监听窗口大小变化，重新调整结果区域高度
    window.addEventListener('resize', function() {
        adjustResultHeight();
    });

    // 监听滚动事件，重新调整结果区域高度
    window.addEventListener('scroll', function() {
        adjustResultHeight();
    });

    // 处理选中的关键词，去掉括号内容
    function processSelectedKeyword(value) {
        if (value === 'empty') {
            return '';
        }
        // 去掉括号和括号内的内容
        return value.replace(/\s*\([^)]*\)/g, '').trim();
    }

    // 构建Google Trends URL
    function buildGoogleTrendsUrl(word, compareWord, dateRange) {
        const baseUrl = 'https://trends.google.com/trends/explore';
        const params = new URLSearchParams({
            'hl': 'en'          // 英文界面
        });

        // 设置日期范围参数
        if (dateRange && dateRange.trim()) {
            params.set('date', dateRange);
        }
        // 注意：Past 12 months 不需要date参数，使用默认值

        // 如果对比词不为空，就添加对比
        if (compareWord && compareWord.trim()) {
            params.set('q', `${word},${compareWord.trim()}`);
        } else {
            // 对比词为空，只搜索单个词条
            params.set('q', word);
        }

        return `${baseUrl}?${params.toString()}`;
    }

    // 打开Google Trends比较
    function openGoogleTrends(word) {
        try {
            if (!word || !word.trim()) {
                showStatus('词条不能为空', 'error');
                return;
            }

            const selectedKeyword = compareWordSelect.value;
            const compareWord = processSelectedKeyword(selectedKeyword);
            const dateRange = dateRangeSelect.value;
            const url = buildGoogleTrendsUrl(word.trim(), compareWord, dateRange);

            // 调试信息
            console.log('Google Trends URL:', url);
            console.log('Date Range:', dateRange);
            console.log('Compare Word:', compareWord);

            chrome.tabs.create({ url: url });

            // 显示状态信息
            const selectedText = dateRangeSelect.options[dateRangeSelect.selectedIndex].text;
            if (compareWord && compareWord.trim()) {
                showStatus(`正在比较 "${word.trim()}" 与 "${compareWord.trim()}" (${selectedText})`, 'success');
            } else {
                showStatus(`正在分析 "${word.trim()}" 的趋势 (${selectedText})`, 'success');
            }
        } catch (error) {
            console.error('打开Google Trends失败:', error);
            showStatus('打开Google Trends失败', 'error');
        }
    }

    // 监听storage变化，自动添加选中的文本
    chrome.storage.onChanged.addListener(function(changes, namespace) {
        if (namespace === 'session' && changes.selectedText) {
            const newText = changes.selectedText.newValue;
            if (newText && enableRightClickCheckbox.checked) {
                addTextToTextarea(newText);
            }
        }
    });

    // 添加文本到textarea的新一行
    function addTextToTextarea(text) {
        // 清理文本，去除多余的空白字符
        const cleanText = text.trim();
        if (!cleanText) return;

        const currentValue = textInput.value.trim();
        const newValue = currentValue ? currentValue + '\n' + cleanText : cleanText;
        textInput.value = newValue;

        // 保存更新后的内容
        chrome.storage.session.set({savedText: newValue});

        // 显示状态
        showStatus(`已添加文本: ${cleanText}`, 'success');

        // 滚动到底部
        textInput.scrollTop = textInput.scrollHeight;

        // 触发input事件，确保实时保存
        textInput.dispatchEvent(new Event('input'));
    }

    // Ahrefs查询功能

    // 域名标准化处理函数
    function normalizeDomain(input) {
        if (!input) return '';

        let domain = input.trim();

        // 移除协议前缀 (https://, http://)
        domain = domain.replace(/^https?:\/\//, '');

        // 移除www.前缀
        domain = domain.replace(/^www\./, '');

        // 移除末尾的路径、查询参数等
        domain = domain.split('/')[0];
        domain = domain.split('?')[0];
        domain = domain.split('#')[0];

        return domain;
    }

    // 构建Ahrefs查询URL
    function buildAhrefsUrl(type, input) {
        if (!input) return '';

        switch (type) {
            case 'kd':
                // KD查询：https://ahrefs.com/keyword-difficulty/?country=us&input=关键词
                return `https://ahrefs.com/keyword-difficulty/?country=us&input=${encodeURIComponent(input)}`;

            case 'backlinks':
                // Back Links查询：https://ahrefs.com/backlink-checker/?input=域名&mode=subdomains
                const normalizedDomain = normalizeDomain(input);
                return `https://ahrefs.com/backlink-checker/?input=${encodeURIComponent(normalizedDomain)}&mode=subdomains`;

            default:
                return '';
        }
    }

    // Ahrefs查询输入验证函数
    function validateAhrefsInput(inputElement, queryType) {
        const inputText = inputElement.value.trim();
        if (!inputText) {
            const queryName = queryType === 'kd' ? 'KD查询' : 'Back Links查询';
            status.textContent = `请输入${queryName}内容`;
            status.style.color = '#dc2626';
            setTimeout(() => {
                status.textContent = '';
            }, 3000);
            return false;
        }
        return true;
    }

    // 执行Ahrefs查询
    function performAhrefsSearch(type, inputElement) {
        if (!validateAhrefsInput(inputElement, type)) {
            return;
        }

        const inputText = inputElement.value.trim();
        const searchUrl = buildAhrefsUrl(type, inputText);

        if (searchUrl) {
            chrome.tabs.create({ url: searchUrl });

            const queryName = type === 'kd' ? 'KD' : 'Back Links';
            status.textContent = `已打开 ${queryName} 查询`;
            status.style.color = '#059669';
            setTimeout(() => {
                status.textContent = '';
            }, 2000);
        }
    }

    // Google搜索功能

    // 输入验证函数
    function validateGoogleSearchInput() {
        const searchText = googleSearchInput.value.trim();
        if (!searchText) {
            status.textContent = '请输入搜索内容';
            status.style.color = '#dc2626';
            setTimeout(() => {
                status.textContent = '';
            }, 3000);
            return false;
        }
        return true;
    }

    // 构建Google搜索URL
    function buildGoogleSearchUrl(searchType, searchText, addQuotes) {
        let query = '';

        switch (searchType) {
            case 'site':
                query = `site:${searchText}`;
                break;
            case 'intitle':
                query = addQuotes ? `intitle:"${searchText}"` : `intitle:${searchText}`;
                break;
            case 'allintitle':
                query = addQuotes ? `allintitle:"${searchText}"` : `allintitle:${searchText}`;
                break;
        }

        return `https://www.google.com/search?q=${encodeURIComponent(query)}`;
    }

    // 打开Google搜索
    function openGoogleSearch(searchType) {
        if (!validateGoogleSearchInput()) {
            return;
        }

        const searchText = googleSearchInput.value.trim();
        const addQuotes = addQuotesCheckbox.checked;
        const searchUrl = buildGoogleSearchUrl(searchType, searchText, addQuotes);

        chrome.tabs.create({ url: searchUrl });

        status.textContent = `已打开 ${searchType} 搜索`;
        status.style.color = '#059669';
        setTimeout(() => {
            status.textContent = '';
        }, 2000);
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 只在有处理结果时才调整高度
        if (processedResult.classList.contains('show')) {
            adjustResultHeight();
        }
    });
});