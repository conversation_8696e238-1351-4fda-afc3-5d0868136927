<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮布局测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 12px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .width-control {
            margin-bottom: 20px;
        }
        
        .width-control input {
            width: 300px;
        }
        
        .demo-area {
            background: #ffffff;
            border: 2px solid #007cba;
            padding: 12px;
            transition: width 0.3s ease;
            min-width: 200px;
            max-width: 600px;
        }
        
        /* 复制扩展中的样式 */
        .button-group {
            display: flex;
            gap: 4px;
            margin-top: 6px;
            flex-wrap: wrap;
        }
        
        .links-button-row {
            flex-wrap: nowrap;
        }
        
        .links-button-row .btn {
            min-width: 80px;
            white-space: nowrap;
        }
        
        .btn {
            flex: 1;
            min-width: 70px;
            padding: 6px 4px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: rgb(89, 89, 89);
            color: white;
            transition: all 0.2s ease;
        }
        
        .btn:hover {
            background: rgb(36, 144, 135);
        }
        
        .btn-primary {
            background: rgb(89, 89, 89);
            color: #ffffff;
            border-color: rgb(89, 89, 89);
        }
        
        .btn-secondary {
            background: rgb(89, 89, 89);
            color: #ffffff;
            border-color: rgb(89, 89, 89);
        }
        
        .btn-warning {
            background: rgb(89, 89, 89);
            color: #ffffff;
            border-color: rgb(89, 89, 89);
        }
        
        .status {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>Links Opener按钮布局测试</h1>
    
    <div class="width-control">
        <label for="widthSlider">调整容器宽度：</label>
        <input type="range" id="widthSlider" min="200" max="600" value="350">
        <span id="widthDisplay">350px</span>
    </div>
    
    <div class="test-container">
        <h3>修复后的布局（期望效果）</h3>
        <div class="demo-area" id="demoArea">
            <textarea placeholder="请粘贴从Excel复制的链接地址（每行一个）" rows="3" style="width: 100%; box-sizing: border-box;"></textarea>
            
            <!-- 第一行：打开链接按钮 -->
            <div class="button-group links-button-row">
                <button class="btn btn-primary">打开链接</button>
            </div>

            <!-- 第二行：清除链接和关闭Tabs按钮 -->
            <div class="button-group links-button-row">
                <button class="btn btn-secondary">清除链接</button>
                <button class="btn btn-warning">关闭Tabs</button>
            </div>
            
            <div class="status">
                第一行：打开链接（单独一行）<br>
                第二行：清除链接 + 关闭Tabs（两个按钮一行）
            </div>
        </div>
    </div>
    
    <div class="test-container">
        <h3>修复前的布局（问题演示）</h3>
        <div class="demo-area" style="width: 350px;">
            <textarea placeholder="请粘贴从Excel复制的链接地址（每行一个）" rows="3" style="width: 100%; box-sizing: border-box;"></textarea>
            
            <!-- 所有按钮在一个组中 -->
            <div class="button-group">
                <button class="btn btn-primary">打开链接</button>
                <button class="btn btn-secondary">清除链接</button>
                <button class="btn btn-warning">关闭Tabs</button>
            </div>
            
            <div class="status">
                问题：当界面放大时，所有按钮挤在一行
            </div>
        </div>
    </div>

    <script>
        const widthSlider = document.getElementById('widthSlider');
        const widthDisplay = document.getElementById('widthDisplay');
        const demoArea = document.getElementById('demoArea');

        widthSlider.addEventListener('input', function() {
            const width = this.value + 'px';
            widthDisplay.textContent = width;
            demoArea.style.width = width;
        });
    </script>
</body>
</html>
