<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .checkbox-container { margin: 10px 0; }
        .progress-container { 
            display: flex; 
            align-items: center; 
            gap: 6px; 
            padding: 6px 8px; 
            background-color: #f8f9fa; 
            border-radius: 4px; 
            border: 1px solid #e5e5e5; 
        }
        .progress-hidden { display: none !important; }
        .btn { padding: 8px 16px; margin: 5px; cursor: pointer; }
        .btn-primary { background: #4285f4; color: white; border: none; }
        .btn-secondary { background: #6c757d; color: white; border: none; }
        textarea { width: 100%; height: 100px; margin: 10px 0; }
        .result-content { margin: 10px 0; }
        .word-item { 
            display: flex; 
            align-items: center; 
            padding: 5px; 
            margin: 2px 0; 
            background: #f5f5f5; 
        }
        .word-text { flex: 1; }
    </style>
</head>
<body>
    <h1>Trends & Links Tools 功能测试</h1>
    
    <div class="test-section">
        <h2>控制数量功能测试</h2>
        
        <div class="checkbox-container">
            <label>
                <input type="checkbox" id="enableRightClick"> 启用右键添加文本
            </label>
            <label style="margin-left: 20px;">
                <input type="checkbox" id="enableQuantityControl"> 启用控制数量
            </label>
        </div>
        
        <textarea id="textInput" placeholder="输入测试文本，每行一个词条"></textarea>
        
        <div>
            <button id="processTextBtn" class="btn btn-primary">处理文本</button>
            <button id="searchAllBtn" class="btn btn-primary">Trends搜索</button>
            <button id="clearTextBtn" class="btn btn-secondary">清除文本</button>
        </div>
        
        <div id="progressContainer" class="progress-container progress-hidden">
            <span class="progress-label">处理进度：</span>
            <span id="progressDisplay" class="progress-display">0/0</span>
        </div>
        
        <div id="resultContent" class="result-content"></div>
        
        <div id="status" style="margin: 10px 0; padding: 10px; background: #e9ecef;"></div>
    </div>

    <script>
        // 模拟Chrome扩展API
        const chrome = {
            storage: {
                session: {
                    set: (data) => console.log('保存到session:', data),
                    get: (keys, callback) => callback({})
                }
            },
            tabs: {
                create: (options) => {
                    console.log('创建标签页:', options.url);
                    return Promise.resolve();
                }
            }
        };

        // 进度控制状态变量
        let currentIndex = 0;
        let totalCount = 0;
        let processedWords = [];

        // 获取DOM元素
        const enableQuantityControlCheckbox = document.getElementById('enableQuantityControl');
        const progressContainer = document.getElementById('progressContainer');
        const progressDisplay = document.getElementById('progressDisplay');
        const textInput = document.getElementById('textInput');
        const processTextBtn = document.getElementById('processTextBtn');
        const searchAllBtn = document.getElementById('searchAllBtn');
        const clearTextBtn = document.getElementById('clearTextBtn');
        const resultContent = document.getElementById('resultContent');
        const status = document.getElementById('status');

        // 进度管理函数
        function updateProgressDisplay() {
            progressDisplay.textContent = `${currentIndex}/${totalCount}`;
        }

        function resetProgress() {
            currentIndex = 0;
            totalCount = 0;
            processedWords = [];
            updateProgressDisplay();
            progressContainer.classList.add('progress-hidden');
        }

        function showProgress() {
            if (enableQuantityControlCheckbox.checked) {
                progressContainer.classList.remove('progress-hidden');
            }
        }

        function hideProgress() {
            progressContainer.classList.add('progress-hidden');
        }

        function showStatus(message, type) {
            status.textContent = message;
            status.style.background = type === 'error' ? '#f8d7da' : 
                                    type === 'success' ? '#d4edda' : '#e9ecef';
        }

        // 简单的文本处理函数
        function processText(text) {
            return text.split('\n')
                      .map(line => line.trim())
                      .filter(line => line && !(/^\d+$/.test(line)))
                      .map(line => line.replace(/-/g, ' '));
        }

        function renderWordList(words) {
            resultContent.innerHTML = words.map((word, index) => 
                `<div class="word-item">
                    <span class="word-text">${word}</span>
                </div>`
            ).join('');
        }

        // 事件监听器
        enableQuantityControlCheckbox.addEventListener('change', function() {
            if (this.checked) {
                showProgress();
            } else {
                hideProgress();
            }
        });

        processTextBtn.addEventListener('click', function() {
            const inputText = textInput.value.trim();
            if (!inputText) {
                showStatus('请输入要处理的文本', 'error');
                return;
            }

            const processedText = processText(inputText);
            if (processedText.length === 0) {
                showStatus('处理后没有有效内容', 'info');
                resetProgress();
            } else {
                showStatus(`成功处理 ${processedText.length} 个词条`, 'success');
                renderWordList(processedText);
                
                processedWords = processedText;
                totalCount = processedText.length;
                currentIndex = 1;
                updateProgressDisplay();
                showProgress();
            }
        });

        searchAllBtn.addEventListener('click', async function() {
            const wordItems = resultContent.querySelectorAll('.word-item');
            if (wordItems.length === 0) {
                showStatus('搜索结果为空', 'info');
                return;
            }

            if (enableQuantityControlCheckbox.checked) {
                await handleQuantityControlledSearch(wordItems);
            } else {
                await handleNormalSearch(wordItems);
            }
        });

        async function handleNormalSearch(wordItems) {
            const totalWords = wordItems.length;
            showStatus(`模拟打开 ${totalWords} 个Google Trends页面...`, 'info');
            
            for (const wordItem of wordItems) {
                const wordText = wordItem.querySelector('.word-text').textContent;
                console.log('打开:', wordText);
                await new Promise(resolve => setTimeout(resolve, 50));
            }
            
            if (enableQuantityControlCheckbox.checked) {
                currentIndex = totalWords + 1; // 设置为总数+1，表示所有词条都已处理完
                updateProgressDisplay();
            }
            
            showStatus(`成功打开 ${totalWords} 个Google Trends页面`, 'success');
        }

        async function handleQuantityControlledSearch(wordItems) {
            const totalWords = wordItems.length;

            if (currentIndex > totalWords) {
                showStatus('没有更多网页可加载', 'info');
                return;
            }

            // currentIndex是1基索引（显示用），需要转换为0基索引（数组用）
            const startIndex = currentIndex - 1; // 转换为0基索引
            const endIndex = Math.min(startIndex + 10, totalWords);
            const batchSize = endIndex - startIndex;

            showStatus(`模拟打开第 ${currentIndex}-${currentIndex + batchSize - 1} 个页面 (共${totalWords}个)...`, 'info');

            for (let i = startIndex; i < endIndex; i++) {
                const wordItem = wordItems[i];
                const wordText = wordItem.querySelector('.word-text').textContent;
                console.log('打开:', wordText);
                await new Promise(resolve => setTimeout(resolve, 50));
            }

            // 更新进度（转换回1基索引用于显示）
            currentIndex = endIndex + 1;
            updateProgressDisplay();

            showStatus(`成功打开 ${batchSize} 个页面 - 进度: ${currentIndex - 1}/${totalWords}`, 'success');
        }

        clearTextBtn.addEventListener('click', function() {
            textInput.value = '';
            enableQuantityControlCheckbox.checked = false;
            resetProgress();
            resultContent.innerHTML = '';
            showStatus('文本内容已清理', 'info');
        });

        // 初始化
        resetProgress();
    </script>
</body>
</html>
