<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL编码测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .test-case {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .url-output {
            font-family: monospace;
            background: #fff;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            word-break: break-all;
            margin: 5px 0;
        }
        .expected {
            color: #059669;
        }
        .actual {
            color: #dc2626;
        }
        .correct {
            color: #059669;
        }
    </style>
</head>
<body>
    <h1>Google搜索URL编码测试</h1>
    
    <div class="test-case">
        <h3>测试用例：Deep Fried Image</h3>
        <p><strong>输入：</strong>"Deep Fried Image"</p>
        <p><strong>引号：</strong>未选中</p>
        
        <h4>allintitle搜索结果：</h4>
        <div class="url-output actual" id="allintitle-result"></div>
        
        <h4>期望的URL格式：</h4>
        <div class="url-output expected">https://www.google.com/search?q=allintitle%3ADeep%20Fried%20Image</div>
        
        <h4>intitle搜索结果：</h4>
        <div class="url-output actual" id="intitle-result"></div>
        
        <h4>期望的URL格式：</h4>
        <div class="url-output expected">https://www.google.com/search?q=intitle%3ADeep%20Fried%20Image</div>
    </div>

    <div class="test-case">
        <h3>测试用例：Deep Fried Image（带引号）</h3>
        <p><strong>输入：</strong>"Deep Fried Image"</p>
        <p><strong>引号：</strong>选中</p>
        
        <h4>allintitle搜索结果：</h4>
        <div class="url-output actual" id="allintitle-quotes-result"></div>
        
        <h4>期望的URL格式：</h4>
        <div class="url-output expected">https://www.google.com/search?q=allintitle%3A%22Deep%20Fried%20Image%22</div>
        
        <h4>intitle搜索结果：</h4>
        <div class="url-output actual" id="intitle-quotes-result"></div>
        
        <h4>期望的URL格式：</h4>
        <div class="url-output expected">https://www.google.com/search?q=intitle%3A%22Deep%20Fried%20Image%22</div>
    </div>

    <div class="test-case">
        <h3>测试用例：site搜索</h3>
        <p><strong>输入：</strong>"example.com"</p>
        
        <h4>site搜索结果：</h4>
        <div class="url-output actual" id="site-result"></div>
        
        <h4>期望的URL格式：</h4>
        <div class="url-output expected">https://www.google.com/search?q=site%3Aexample.com</div>
    </div>

    <script>
        // 修复后的URL构建函数
        function buildGoogleSearchUrl(searchType, searchText, addQuotes) {
            let query = '';
            
            switch (searchType) {
                case 'site':
                    query = `site:${searchText}`;
                    break;
                case 'intitle':
                    query = addQuotes ? `intitle:"${searchText}"` : `intitle:${searchText}`;
                    break;
                case 'allintitle':
                    query = addQuotes ? `allintitle:"${searchText}"` : `allintitle:${searchText}`;
                    break;
            }
            
            return `https://www.google.com/search?q=${encodeURIComponent(query)}`;
        }

        // 运行测试
        function runTests() {
            const testText = "Deep Fried Image";
            
            // 测试1：allintitle 无引号
            const allintitleUrl = buildGoogleSearchUrl('allintitle', testText, false);
            document.getElementById('allintitle-result').textContent = allintitleUrl;
            
            // 测试2：intitle 无引号
            const intitleUrl = buildGoogleSearchUrl('intitle', testText, false);
            document.getElementById('intitle-result').textContent = intitleUrl;
            
            // 测试3：allintitle 有引号
            const allintitleQuotesUrl = buildGoogleSearchUrl('allintitle', testText, true);
            document.getElementById('allintitle-quotes-result').textContent = allintitleQuotesUrl;
            
            // 测试4：intitle 有引号
            const intitleQuotesUrl = buildGoogleSearchUrl('intitle', testText, true);
            document.getElementById('intitle-quotes-result').textContent = intitleQuotesUrl;
            
            // 测试5：site搜索
            const siteUrl = buildGoogleSearchUrl('site', 'example.com', false);
            document.getElementById('site-result').textContent = siteUrl;
            
            // 验证结果
            checkResults();
        }

        function checkResults() {
            const tests = [
                {
                    id: 'allintitle-result',
                    expected: 'https://www.google.com/search?q=allintitle%3ADeep%20Fried%20Image'
                },
                {
                    id: 'intitle-result',
                    expected: 'https://www.google.com/search?q=intitle%3ADeep%20Fried%20Image'
                },
                {
                    id: 'allintitle-quotes-result',
                    expected: 'https://www.google.com/search?q=allintitle%3A%22Deep%20Fried%20Image%22'
                },
                {
                    id: 'intitle-quotes-result',
                    expected: 'https://www.google.com/search?q=intitle%3A%22Deep%20Fried%20Image%22'
                },
                {
                    id: 'site-result',
                    expected: 'https://www.google.com/search?q=site%3Aexample.com'
                }
            ];

            tests.forEach(test => {
                const element = document.getElementById(test.id);
                const actual = element.textContent;
                if (actual === test.expected) {
                    element.className = 'url-output correct';
                    element.title = '✅ 正确';
                } else {
                    element.className = 'url-output actual';
                    element.title = '❌ 不匹配期望结果';
                }
            });
        }

        // 页面加载后运行测试
        window.addEventListener('load', runTests);
    </script>
</body>
</html>
