# Google搜索功能持久化测试指南

## 测试步骤

### 1. 基本功能测试
1. 打开Chrome扩展的侧边栏
2. 切换到"Links Opener"标签页
3. 在Google搜索输入框中输入测试内容，例如："test search"
4. 选中"添加引号"checkbox
5. 点击不同的搜索按钮验证功能正常

### 2. 持久化功能测试
1. 在Google搜索输入框中输入内容："持久化测试"
2. 选中"添加引号"checkbox
3. 切换到其他网页（例如打开新标签页或访问其他网站）
4. 重新打开Chrome扩展的侧边栏
5. 检查Google搜索输入框中的内容是否保持为"持久化测试"
6. 检查"添加引号"checkbox是否仍然选中

### 3. 跨标签页测试
1. 在扩展中输入内容并选择checkbox
2. 切换到"Text Process"标签页
3. 再切换回"Links Opener"标签页
4. 验证Google搜索内容是否保持

### 4. 浏览器重启测试
1. 在Google搜索输入框中输入内容
2. 选择checkbox状态
3. 关闭浏览器
4. 重新打开浏览器和扩展
5. 验证内容是否保持（注意：session storage在浏览器重启后会清空，这是正常的）

## 预期结果

### ✅ 应该保持的情况：
- 切换网页后内容保持
- 切换扩展标签页后内容保持
- 关闭/重新打开扩展后内容保持

### ❌ 不会保持的情况（正常行为）：
- 浏览器完全重启后（session storage特性）

## 测试用例

### 测试用例1：基本输入保存
- 输入："example.com"
- checkbox：未选中
- 预期：切换网页后内容保持

### 测试用例2：引号功能保存
- 输入："search term"
- checkbox：选中
- 预期：切换网页后内容和checkbox状态都保持

### 测试用例3：空内容处理
- 输入：""（空）
- checkbox：选中
- 预期：切换网页后checkbox状态保持，输入框保持空

### 测试用例4：特殊字符处理
- 输入："test & search"
- checkbox：未选中
- 预期：特殊字符正确保存和恢复

## 故障排除

如果持久化功能不工作：
1. 检查Chrome扩展权限是否包含"storage"
2. 检查浏览器控制台是否有错误信息
3. 验证chrome.storage.session API是否可用
4. 检查扩展是否正确加载

## 实现细节

持久化功能使用以下存储键：
- `savedGoogleSearchText`: 保存搜索输入框内容
- `savedAddQuotes`: 保存引号checkbox状态

这些数据存储在chrome.storage.session中，在浏览器会话期间保持，但浏览器重启后会清空。
