<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>链接批量打开&文本处理工具</title>
    <link rel="stylesheet" href="styles.css" />
  </head>
  <body>
    <div class="container">
      <!-- <h2>链接批量打开器</h2> -->

      <!-- 标签页导航 -->
      <div class="tab-navigation">
        <button class="tab-btn active" data-tab="link-tab">
          <!-- <span class="tab-title">链接打开</span> -->
          <span class="tab-subtitle">Links Opener</span>
        </button>
        <button class="tab-btn" data-tab="text-tab">
          <!-- <span class="tab-title">文本处理</span> -->
          <span class="tab-subtitle">Text Process</span>
        </button>
        <button class="tab-btn" data-tab="root-analysis-tab">
          <span class="tab-subtitle">Root Analysis</span>
        </button>
      </div>

      <!-- 链接打开功能 -->
      <div id="link-tab" class="tab-content active">
        <!-- <p>请粘贴从Excel复制的链接地址（每行一个）</p> -->

        <textarea
          id="urlInput"
          placeholder="请粘贴从Excel复制的链接地址（每行一个）
https://example1.com
https://example2.com
https://example3.com"
          rows="8"
          cols="50"
        ></textarea>

        <!-- 第一行：打开链接按钮 -->
        <div class="button-group links-button-row">
          <button id="openAllBtn" class="btn btn-primary">打开链接</button>
        </div>

        <!-- 第二行：清除链接和关闭Tabs按钮 -->
        <div class="button-group links-button-row">
          <button id="clearBtn" class="btn btn-secondary">清除链接</button>
          <button
            id="closeOpenedBtn"
            class="btn btn-warning"
            style="display: none"
          >
            关闭Tabs
          </button>
        </div>

        <!-- Google搜索功能区域 -->
        <div class="google-search-section">
          <input
            type="text"
            id="googleSearchInput"
            class="google-search-input"
            placeholder="输入搜索内容（用于Google搜索语法）"
          />

          <div class="google-search-controls">
            <label class="google-search-checkbox-label">
              <input type="checkbox" id="addQuotesCheckbox" class="google-search-checkbox">
              <span class="google-search-checkbox-text">添加引号</span>
            </label>
          </div>

          <div class="google-search-buttons">
            <button id="siteSearchBtn" class="btn btn-google-search">site</button>
            <button id="intitleSearchBtn" class="btn btn-google-search">intitle</button>
            <button id="allintitleSearchBtn" class="btn btn-google-search">allintitle</button>
          </div>
        </div>
      </div>

      <!-- 文本处理功能 -->
      <div id="text-tab" class="tab-content">
        <!-- <p>粘贴Excel列内容，自动处理"-"符号并过滤纯数字</p> -->


        <div class="date-range-container">
          <label for="dateRangeSelect" class="date-range-label">Date Range：</label>
          <select id="dateRangeSelect" class="date-range-select">
            <option value="now 7-d">Past 7 days</option>
            <option value="today 1-m">Past 30 days</option>
            <option value="today 3-m">Past 90 days</option>
            <option value="">Past 12 months</option>
            <option value="today 5-y">Past 5 years</option>
          </select>
        </div>

        <div class="compare-word-container">
          <label for="compareWordSelect" class="compare-word-label"
            >Comparison KeyWord：</label
          >
          <select id="compareWordSelect" class="compare-word-select">
            <option value="empty">empty</option>
            <option value="Gpts( 5k )" selected>Gpts( 5k )</option>
            <option value="good morning images( 130k )">good morning images( 130k )</option>
            <option value="happy birthday image( 50k )">happy birthday image( 50k )</option>
            <option value="baby shower( 25k )">baby shower( 25k )</option>
            <option value="image to text converter( 10k )">image to text converter( 10k )</option>
            <option value="ai image upscaler( 3k )">ai image upscaler( 3k )</option>
            <option value="random images( 2k )">random images( 2k )</option>
            <option value="banana png( 1.1k )">banana png( 1.1k )</option>
            <option value="translate text from( 1k )">translate text from( 1k )</option>
            <option value="conversational( 0.6k)">conversational( 0.6k)</option>
          </select>
        </div>

        <div class="right-click-container">
          <label class="right-click-label">
            <input type="checkbox" id="enableRightClick" class="right-click-checkbox">
            <span class="right-click-text">启用右键添加文本</span>
          </label>
          <label class="right-click-label">
            <input type="checkbox" id="enableQuantityControl" class="right-click-checkbox">
            <span class="right-click-text">启用控制数量</span>
          </label>
        </div>

        <textarea
          id="textInput"
          placeholder="粘贴Excel列内容，自动处理 - 符号并过滤纯数字
例如：
word-one
12345"
          rows="8"
          cols="50"
        ></textarea>
        
        <div class="button-group">
          <button id="processTextBtn" class="btn btn-primary">处理文本</button>
          <button id="searchAllBtn" class="btn btn-warning">Trends搜索</button>
          <button id="closeTrendsBtn" class="btn btn-secondary">关闭Tabs</button>
          <button id="clearTextBtn" class="btn btn-secondary">清除文本</button>
        </div>

        <!-- 进度显示区域 -->
        <div id="progressContainer" class="progress-container progress-hidden">
          <span class="progress-label">处理进度：</span>
          <span id="progressDisplay" class="progress-display">0/0</span>
        </div>

        <div id="processedResult" class="processed-result">
          <h4>Result：</h4>
          <div id="resultContent" class="result-content"></div>
        </div>
      </div>

      <!-- Root Analysis功能 -->
      <div id="root-analysis-tab" class="tab-content">
        <!-- 复用Date Range和Comparison KeyWord -->
        <div class="compare-word-container">
          <label for="rootCompareWordSelect" class="compare-word-label">Comparison KeyWord：</label>
          <select id="rootCompareWordSelect" class="compare-word-select">
            <option value="empty">empty</option>
            <option value="Gpts( 5k )" selected>Gpts( 5k )</option>
            <option value="good morning images( 130k )">good morning images( 130k )</option>
            <option value="happy birthday image( 50k )">happy birthday image( 50k )</option>
            <option value="baby shower( 25k )">baby shower( 25k )</option>
            <option value="image to text converter( 10k )">image to text converter( 10k )</option>
            <option value="ai image upscaler( 3k )">ai image upscaler( 3k )</option>
            <option value="random images( 2k )">random images( 2k )</option>
            <option value="banana png( 1.1k )">banana png( 1.1k )</option>
            <option value="translate text from( 1k )">translate text from( 1k )</option>
            <option value="conversational( 0.6k)">conversational( 0.6k)</option>
          </select>
        </div>

        <div class="date-range-container">
          <label for="rootDateRangeSelect" class="date-range-label">Date Range：</label>
          <select id="rootDateRangeSelect" class="date-range-select">
            <option value="now 7-d">Past 7 days</option>
            <option value="today 1-m">Past 30 days</option>
            <option value="today 3-m">Past 90 days</option>
            <option value="">Past 12 months</option>
            <option value="today 5-y">Past 5 years</option>
          </select>
        </div>

        <!-- 序号和数量输入 -->
        <div class="root-input-container">
          <label class="root-input-label">打开链接：打开从</label>
          <input type="number" id="startIndexInput" class="root-input" min="0">
          <label class="root-input-label">开始的</label>
          <input type="number" id="countInput" class="root-input" min="1">
          <label class="root-input-label">个链接</label>
          <button id="openRootLinksBtn" class="btn btn-primary">打开链接</button>
          <button id="closeRootTrendsBtn" class="btn btn-secondary">关闭Tabs</button>
        </div>

        <!-- 词汇列表 -->
        <div id="rootWordsList" class="root-words-list">
          <!-- 动态生成词汇列表 -->
        </div>

        <!-- 编辑按钮 -->
        <div class="root-edit-container">
          <button id="editRootWordsBtn" class="btn btn-secondary">编辑</button>
        </div>
      </div>

      <div id="status" class="status"></div>
    </div>

    <!-- 编辑Root Words模态框 -->
    <div id="editRootWordsModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>编辑Root Words</h3>
          <span class="modal-close">&times;</span>
        </div>
        <div class="modal-body">
          <p>每行一个词汇：</p>
          <textarea id="editRootWordsTextarea" rows="15" cols="50"></textarea>
        </div>
        <div class="modal-footer">
          <button id="saveRootWordsBtn" class="btn btn-primary">保存</button>
          <button id="cancelEditBtn" class="btn btn-secondary">取消</button>
          <button id="resetToDefaultBtn" class="btn btn-warning">恢复默认</button>
        </div>
      </div>
    </div>

    <script src="popup.js"></script>
  </body>
</html>
